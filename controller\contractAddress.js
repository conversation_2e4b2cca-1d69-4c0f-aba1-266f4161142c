const ContractAddress = require("../model/contractAddressDCA");

const saveContractAddress = async (req, res) => {
  try {
    const { contractAddress } = req.body;

    if (!contractAddress) {
      return res.status(400).json({ error: "Contract address is required" });
    }

    const newAddress = new ContractAddress({ contractAddress });
    await newAddress.save();

    res
      .status(201)
      .json({ message: "Contract address saved", data: newAddress });
  } catch (error) {
    res
      .status(500)
      .json({ error: "Internal server error", details: error.message });
  }
};

const getContractAddress = async (req, res) => {
  try {
    const addresses = await ContractAddress.find();
    if (addresses.length > 0) {
      res.status(200).json(addresses[0]);
    } else {
      res.status(404).json({ error: "No addresses found" });
    }
  } catch (error) {
    res
      .status(500)
      .json({ error: "Internal server error", details: error.message });
  }
};

module.exports = {
  saveContractAddress,
  getContractAddress,
};
