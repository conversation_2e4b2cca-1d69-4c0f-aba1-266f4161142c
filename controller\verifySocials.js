const SocialVerification = require('../model/verifySocials');

exports.verifySocials = async (req, res) => {
    const { walletAddress, telegram, twitter, discord, linkedin, instagram, youtube } = req.body;

    if (!walletAddress) return res.status(400).json({ message: 'User ID is required' });

    try {
        const existing = await SocialVerification.findOne({ walletAddress });

        if (existing) {
            // Update existing
            existing.telegram = telegram || existing.telegram;
            existing.twitter = twitter || existing.twitter;
            existing.discord = discord || existing.discord;
            existing.linkedin = linkedin || existing.linkedin;
            existing.instagram = instagram || existing.instagram;
            existing.youtube = youtube || existing.youtube;
            await existing.save();
            return res.status(200).json({ message: 'Socials updated', data: existing });
        }

        // Create new
        const socials = new SocialVerification({
            walletAddress, telegram, twitter, discord, linkedin, instagram, youtube
        });

        await socials.save();
        res.status(201).json({ message: 'Socials saved', data: socials });

    } catch (err) {
        console.error(err);
        res.status(500).json({ message: 'Server error' });
    }
}

exports.getSocialsByWallet = async (req, res) => {
    const { walletAddress } = req.params;

    if (!walletAddress) {
        return res.status(400).json({ message: 'Wallet address is required' });
    }

    try {
        const socials = await SocialVerification.findOne({ walletAddress });

        if (!socials) {
            return res.status(404).json({ message: 'No social accounts found for this wallet address' });
        }

        return res.status(200).json({ message: 'Social accounts retrieved successfully', data: socials });

    } catch (err) {
        console.error('Error fetching socials:', err);
        return res.status(500).json({ message: 'Server error' });
    }
};

