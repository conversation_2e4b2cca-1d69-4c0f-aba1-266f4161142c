const express = require("express");
const {
  createDCA,
  getDCA,
  startDCA,
  stopDCA,
  getDCAHistory,
  getDCAListWithOrders,
  getDcaDetailsWithHistory,
  getActiveOrder,
  getAllDCAOrderForAdmin,
  withdrawDCA
} = require("../controller/DCA");

const router = express.Router();

router.post("/create-dca-order", createDCA);
router.post("/get-dca-order", getDCA);
router.post("/get-dca-active-order", getActiveOrder);
router.post("/startDCA", startDCA);
router.post("/stopDCA/:dcaId", stopDCA);
router.get("/history/:walletAddress", getDCAHistory);
router.get("/dcas/:walletAddress", getDCAListWithOrders);
router.get("/dca-history/:id", getDcaDetailsWithHistory);
router.get("/getAllDCAOrderForAdmin", getAllDCAOrderForAdmin);
router.post('/:dcaId/withdraw', withdrawDCA);

module.exports = router;
