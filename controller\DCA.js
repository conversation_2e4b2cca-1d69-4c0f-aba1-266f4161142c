const axios = require("axios");
const asyncHandler = require("express-async-handler");
const { DateTime } = require("luxon");
const DCA = require("../model/DCA");
const DCAHistory = require("../model/DCAHistory");
const { paginate } = require("../utils");

exports.createDCA = async (req, res) => {
  try {
    console.log("this is order", req.body);
    // Validate required input data
    if (!req.body.user || !req.body.inputMint || !req.body.outputMint) {
      return res.status(400).json({
        success: false,
        message: "Missing required fields: user, inputMint, outputMint",
      });
    }

    // Extract parameters with defaults
    let requestData;

    requestData = {
      user: req.body.user,
      inputMint: req.body.inputMint,
      outputMint: req.body.outputMint,
      params: {
        time: {
          inAmount: req.body.inAmount, // Default: 1 SOL
          numberOfOrders: req.body.numberOfOrders, // Default: 2 orders
          interval: req.body.interval, // Default: 24 hours
          maxPrice: req.body.maxPrice === 0 ? null : req.body.maxPrice,
          minPrice: req.body.minPrice === 0 ? null : req.body.minPrice,
          startAt: null,
        },
      },
    };

    const config = {
      method: "post",
      url: "https://lite-api.jup.ag/recurring/v1/createOrder",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      data: requestData,
      timeout: 10000,
    };

    const response = await axios(config);

    if (response.data) {
      return res.status(200).json({
        success: true,
        data: response.data,
        message: "DCA order created successfully",
      });
    } else {
      return res.status(500).json({
        success: false,
        message: "API request succeeded but response indicates failure",
        data: response.data,
      });
    }
  } catch (error) {
    console.error("Error in createDCA:", error);

    if (error.response) {
      // API responded with error status
      return res.status(error.response.status || 500).json({
        success: false,
        message: error.response.data?.error || "Error creating DCA order",
        details: error.response.data,
      });
    } else if (error.request) {
      // No response received
      return res.status(503).json({
        success: false,
        message: "No response received from DCA service",
      });
    } else {
      // Setup error
      return res.status(500).json({
        success: false,
        message: error.message || "Error setting up DCA request",
      });
    }
  }
};

exports.getDCA = async (req, res) => {
  try {
    // Extract query parameters
    const {
      user,
      recurringType = "all",
      orderStatus = "active",
      page = 0,
      limit,
      includeFailedTX = false,
    } = req.body;

    // Validate required parameters
    if (!user) {
      return res
        .status(400)
        .json({ error: "Wallet address (user) is required" });
    }

    // Prepare parameters for the API call
    const params = {
      user,
      recurringType,
      orderStatus,
      page: parseInt(page),
      includeFailedTX: includeFailedTX === "true",
    };

    // Add limit if provided
    if (limit) {
      params.limit = parseInt(limit);
    }

    // Get recurring orders
    const orders = await getRecurringOrders(params);

    // Return the results
    res.status(200).json({
      success: true,
      data: orders,
      meta: {
        count: orders.length,
        page: parseInt(page),
        ...(limit && { limit: parseInt(limit) }),
      },
    });
  } catch (error) {
    console.error("Error in getDCA:", error);
    res.status(500).json({
      success: false,
      error: error.message || "Internal server error",
    });
  }
};

exports.getActiveOrder = async (req, res) => {
  try {
    const response = await axios.get(
      `https://lite-api.jup.ag/recurring/v1/getRecurringOrders?user=${req.body.user}&orderStatus=active&recurringType=time&includeFailedTx=true`
    );
    console.log("comming in this", response);

    return res.status(200).json(response.data);
  } catch (error) {
    res.status(500).json(error);
  }
};

/**
 * Helper function to fetch recurring orders from Jupiter API
 */
async function getRecurringOrders(params) {
  const baseUrl = "https://lite-api.jup.ag/recurring/v1/getRecurringOrders";

  const requestParams = {
    user: params.user,
    recurringType: params.recurringType,
    orderStatus: params.orderStatus,
    page: params.page,
    includeFailedTx: params.includeFailedTX,
    ...(params.limit && { limit: params.limit }),
  };

  try {
    const response = await axios.get(baseUrl, {
      params: requestParams,
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      timeout: 10000,
    });

    return response.data.orders || [];
  } catch (error) {
    console.error("Error fetching recurring orders:", {
      message: error.message,
      response: error.response?.data,
      requestParams,
    });
    throw new Error("Failed to fetch recurring orders");
  }
}

exports.startDCA = asyncHandler(async (req, res) => {
  try {
    const {
      walletAddress,
      inputTokenAddress,
      tokenInName,
      tokenOutName,
      outPutDecimals,
      inputDecimals,
      outPutSymbol,
      inputSymbol,
      ChainId,
      outputTokenAddress,
      category,
      amount,
      allocationCurrency,
      currency,
      parts,
      action,
      minPrice,
      maxPrice,
      totalDuration,
      durationUnit,
      portfolioIncreaseAmount,
    } = req.body;

    if (
      !walletAddress ||
      !inputTokenAddress ||
      !outputTokenAddress ||
      !tokenInName ||
      !tokenOutName ||
      !ChainId ||
      !category ||
      !amount ||
      !currency ||
      !action ||
      !allocationCurrency
    ) {
      return res.status(400).json({ message: "Missing required fields" });
    }

    const now = DateTime.now();
    const validUnits = ["minutes", "hours", "days", "weeks", "months"];

    if (category === "time") {
      if (!durationUnit || !parts || !totalDuration) {
        return res.status(400).json({
          message:
            "totalDuration, durationUnit, and parts are required for time-based DCA.",
        });
      }
      if (!validUnits.includes(durationUnit)) {
        return res.status(400).json({
          message:
            "Invalid durationUnit. Use: minutes, hours, days, weeks, months.",
        });
      }

      let amountPerPart = amount / parts;
      let tempTime = now;
      const executionTimes = [];
      const fixedInterval = totalDuration;

      for (let i = 1; i <= parts; i++) {
        if (durationUnit === "minutes")
          tempTime = tempTime.plus({ minutes: fixedInterval });
        else if (durationUnit === "hours")
          tempTime = tempTime.plus({ hours: fixedInterval });
        else if (durationUnit === "days")
          tempTime = tempTime.plus({ days: fixedInterval });
        else if (durationUnit === "weeks")
          tempTime = tempTime.plus({ weeks: fixedInterval });
        else if (durationUnit === "months")
          tempTime = tempTime.plus({ months: fixedInterval });

        executionTimes.push(tempTime.toISO());
      }

      const newDCA = new DCA({
        walletAddress,
        inputTokenAddress,
        outputTokenAddress,
        tokenInName,
        tokenOutName,
        ChainId,
        category,
        amount,
        allocationCurrency,
        currency,
        totalDuration,
        durationUnit,
        parts,
        action,
        completedParts: 0,
        status: "active",
        startTime: now.toISO(),
        amountPerPart,
        executionTimes,
        minPrice,
        maxPrice,
        outPutDecimals,
        inputDecimals,
        outPutSymbol,
        inputSymbol,
      });

      await newDCA.save();

      await DCAHistory.create({
        dcaId: newDCA._id,
        walletAddress: newDCA.walletAddress,
        asset: newDCA.tokenOutName,
        dateTime: new Date(),
        inputAmount: 0,
        pricePerCoin: 0,
        outputAmount: 0,
        transactionHash: "DCA Created",
        gasUsed: "0",
        chainId: newDCA.ChainId,
        platform:
          newDCA.ChainId === 1
            ? "Ethereum"
            : newDCA.ChainId === 56
            ? "BSC"
            : "Unknown",
        totalInvested: 0,
        totalHoldings: 0,

        inputTokenAddress: newDCA.inputTokenAddress,
        outputTokenAddress: newDCA.outputTokenAddress,
      });
      return res.status(200).json({
        message: "DCA Time strategy created successfully (Fixed Interval Gap)",
        dca: newDCA,
      });
    }

    if (category === "price") {
      if (!totalDuration || !durationUnit || !portfolioIncreaseAmount) {
        return res.status(400).json({
          message:
            "totalDuration, durationUnit, and portfolioIncreaseAmount are required for price-based DCA.",
        });
      }
      if (!validUnits.includes(durationUnit)) {
        return res.status(400).json({
          message:
            "Invalid durationUnit. Use: minutes, hours, days, weeks, months.",
        });
      }

      const newDCA = new DCA({
        walletAddress,
        inputTokenAddress,
        outputTokenAddress,
        tokenInName,
        tokenOutName,
        ChainId,
        category,
        amount,
        allocationCurrency,
        currency,
        totalDuration,
        durationUnit,
        action,
        completedParts: 0,
        status: "active",
        startTime: now.toISO(),
        lastExecutionTime: now.toISO(),
        portfolioIncreaseAmount,
        outPutDecimals,
        inputDecimals,
        outPutSymbol,
        inputSymbol,
      });

      await newDCA.save();

      await DCAHistory.create({
        dcaId: newDCA._id,
        walletAddress: newDCA.walletAddress,
        asset: newDCA.tokenOutName,
        dateTime: new Date(),
        inputAmount: 0,
        pricePerCoin: 0,
        outputAmount: 0,
        transactionHash: "DCA Created",
        gasUsed: "0",
        chainId: newDCA.ChainId,
        platform:
          newDCA.ChainId === 1
            ? "Ethereum"
            : newDCA.ChainId === 56
            ? "BSC"
            : "Unknown",
        totalInvested: 0,
        totalHoldings: 0,
        inputTokenAddress: newDCA.inputTokenAddress,
        outputTokenAddress: newDCA.outputTokenAddress,
      });

      return res.status(200).json({
        message:
          "DCA Price strategy created successfully (Time-Based Execution)",
        dca: newDCA,
      });
    }
  } catch (error) {
    console.error("Error creating DCA:", error);
    return res.status(500).json({ message: "Server error" });
  }
});

exports.stopDCA = asyncHandler(async (req, res) => {
  try {
    const { dcaId } = req.params;

    if (!dcaId) {
      return res.status(400).json({ message: "DCA ID is required" });
    }

    const dca = await DCA.findById(dcaId);

    if (!dca) {
      return res.status(404).json({ message: "DCA not found" });
    }

    if (dca.status === "completed" || dca.status === "stopped") {
      return res
        .status(400)
        .json({ message: "DCA strategy is already stopped." });
    }

    dca.status = "stopped";
    await dca.save();
    return res.status(200).json({ message: "DCA stopped successfully" });
  } catch (error) {
    console.error("Error stopping DCA:", error);
    return res.status(500).json({ message: "Server error" });
  }
});

exports.getDCAHistory = asyncHandler(async (req, res) => {
  try {
    const { walletAddress } = req.params;

    if (!walletAddress) {
      return res.status(400).json({
        success: false,
        message: "Wallet address is required.",
      });
    }

    const history = await DCAHistory.find({
      walletAddress: walletAddress,
    }).sort({ dateTime: -1 });

    const formatted = history.map((doc) => ({
      asset: doc.asset,
      dateTime: doc.dateTime,
      amount: doc.outputAmount,
      pricePerCoin: doc.pricePerCoin,
      totalAmount: doc.inputAmount,
      transactionFees: doc.gasUsed,
      platform: doc.platform,
      totalInvested: doc.totalInvested,
      totalHoldings: doc.totalHoldings,
    }));

    return res.status(200).json({
      data: formatted,
    });
  } catch (error) {
    console.error("Error fetching DCA history:", error);
    return res.status(500).json({
      message: "Server error while retrieving DCA history.",
    });
  }
});

exports.getDCAListWithOrders = asyncHandler(async (req, res) => {
  try {
    const { walletAddress } = req.params;

    // Fetch DCAs for the wallet in descending order of creation
    const dcas = await DCA.find({ walletAddress: walletAddress }).sort({ createdAt: -1 });

    // const result = await Promise.all(
    //   dcas.map(async (dca) => {
    //     let interval = null;
    //     if (dca.category === "time") {
    //       interval = `${dca.totalDuration} ${dca.durationUnit}`;
    //     } else if (dca.category === "price") {
    //       interval = dca.priceInterval;
    //     }

    //     const history = await DCAHistory.findOne({ dcaId: dca._id });
    //     console.log("dca history", history);

    //     return {
    //       id: dca._id,
    //       inputToken: dca.allocationCurrency,
    //       outputToken: dca.currency,
    //       amount: dca.amount,
    //       interval,
    //       orders: dca.parts,
    //       inputTokenAddress: dca.inputTokenAddress,
    //       outputTokenAddress: dca.outputTokenAddress,
    //     };
    //   })
    // );

    return res.status(200).json({
      data: dcas,
    });
  } catch (error) {
    return res.status(500).json({
      message: "An error occurred while fetching DCA list with orders.",
      error: error.message,
    });
  }
});

exports.getDcaDetailsWithHistory = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;

    const dca = await DCA.findById(id);
    if (!dca) {
      return res.status(404).json({ message: "DCA not found" });
    }

    // Determine interval
    let interval = null;
    if (dca.category === "time") {
      interval = `${dca.totalDuration} ${dca.durationUnit}`;
    } else if (dca.category === "price") {
      interval = dca.priceInterval;
    }

    // Fetch execution history records
    const historyRecords = await DCAHistory.find({ dcaId: id }).sort({
      dateTime: 1,
    });

    // Prepare execution history response
    const executionHistory = dca.executionTimes.map((scheduledTime, index) => {
      const executed = historyRecords[index] || {};
      return {
        part: index + 1,
        amount: `${dca.amountPerPart}`,
        tokenAmount: `${dca.amountPerPart} ${dca.allocationCurrency}`,
        dateTime: scheduledTime,
      };
    });

    // Final combined response
    return res.status(200).json({
      data: {
        inputToken: dca.allocationCurrency,
        outputToken: dca.currency,
        amount: dca.amount,
        interval: interval,
        orders: dca.parts,
        inputTokenAddress: dca.inputTokenAddress,
        outputTokenAddress: dca.outputTokenAddress,
        executionHistory: {
          totalParts: dca.parts,
          totalAmount: dca.amount,
          records: executionHistory,
        },
      },
    });
  } catch (error) {
    console.error("Error fetching DCA details:", error);
    return res.status(500).json({
      message: "An error occurred while fetching DCA details and history.",
      error: error.message,
    });
  }
});

exports.dcaHistory = asyncHandler(async (req, res) => {
  try {
    const { walletAddress } = req.params;

    if (!walletAddress) {
      return res.status(400).json({
        success: false,
        message: "Wallet address is required.",
      });
    }

    const history = await DCAHistory.find({
      walletAddress: walletAddress,
    }).sort({ dateTime: -1 });

    const formatted = history.map((doc) => ({
      inputT: doc.asset,
      dateTime: doc.dateTime,
      amount: doc.outputAmount,
      pricePerCoin: doc.pricePerCoin,
      totalAmount: doc.inputAmount,
      transactionFees: doc.gasUsed,
      platform: doc.platform,
      totalInvested: doc.totalInvested,
      totalHoldings: doc.totalHoldings,
    }));

    return res.status(200).json({
      data: formatted,
    });
  } catch (error) {
    console.error("Error fetching DCA history:", error);
    return res.status(500).json({
      message: "Server error while retrieving DCA history.",
    });
  }
});

exports.getAllDCAOrderForAdmin = asyncHandler(async (req, res) => {
  try {
    const {page} = req.query;
    const dcas = await DCA.find();
    const { paginatedItems, totalPages, currentPage, totalItems } = paginate(dcas, 15, page);
    return res.status(200).json({
      data: paginatedItems,
      totalPages,
      currentPage,
      totalItems,
    });
  } catch (error) {
    console.error("Error fetching DCA history:", error);
    return res.status(500).json({
      message: "Server error while retrieving DCA history.",
    });
  }
});

// Withdraw unexecuted DCA funds to user
exports.withdrawDCA = asyncHandler(async (req, res) => {
  try {
    const { dcaId } = req.params;
    console.log("this is dca",dcaId)
    if (!dcaId) {
      return res.status(400).json({ message: "DCA ID is required" });
    }
    const dca = await DCA.findById(dcaId);
    console.log("this is dca object",dcaId)
    if (!dca) {
      return res.status(404).json({ message: "DCA not found" });
    }

    if (dca.status === "withdrawn") {
      return res.status(400).json({ message: "Funds already withdrawn for this DCA." });
    }
    const remainingParts = dca.parts - (dca.completedParts || 0);
    const remainingAmount = remainingParts * dca.amountPerPart;
    if (remainingAmount <= 0) {
      return res.status(400).json({ message: "No funds available for withdrawal." });
    }
    // Transfer logic (ERC20 or native token)
    const { transferToken } = require("../services/DCAEthSwap");
    const isNative = dca.inputTokenAddress.toLowerCase() === "******************************************";
    const transferResult = await transferToken({
      to: dca.walletAddress,
      amount: remainingAmount,
      tokenAddress: dca.inputTokenAddress,
      decimals: dca.inputDecimals,
      isNative,
      chainId: 1,
      privateKey: process.env.DCAPRIVATEKEY,
    });
    if (transferResult.error) {
      return res.status(500).json({ message: "Transfer failed", details: transferResult.error });
    }
    dca.status = "withdrawn";
    await dca.save();
    return res.status(200).json({
      message: "Withdrawal successful",
      amount: remainingAmount,
      txHash: transferResult.transactionHash,
    });
  } catch (error) {
    console.error("Error withdrawing DCA funds:", error);
    return res.status(500).json({ message: "Server error" });
  }
});
