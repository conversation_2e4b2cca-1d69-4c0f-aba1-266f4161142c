{"dependencies": {"@google-cloud/storage": "^7.16.0", "@metaplex-foundation/mpl-token-metadata": "^3.4.0", "@moonpay/moonpay-node": "^0.2.9", "@moralisweb3/common-evm-utils": "^2.27.2", "@project-serum/anchor": "^0.26.0", "@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.98.0", "@uniswap/permit2-sdk": "^1.3.1", "@uniswap/sdk": "^3.0.3", "@uniswap/sdk-core": "^4.0.10", "@uniswap/smart-order-router": "^3.20.1", "axios": "^1.9.0", "bs58": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "ethers": "^5.3.1", "express": "^4.21.2", "express-async-handler": "^1.2.0", "firebase-admin": "^13.2.0", "jsonwebtoken": "^9.0.2", "luxon": "^3.6.1", "mongoose": "^8.12.1", "moralis": "^2.27.2", "multer": "^2.0.1", "node-cron": "^3.0.3", "nodemon": "^3.1.9", "redis": "^5.1.0", "socket.io": "^4.8.1", "swagger-autogen": "^2.23.7", "swagger-ui-express": "^5.0.1"}, "scripts": {"start": "nodemon index.js"}}