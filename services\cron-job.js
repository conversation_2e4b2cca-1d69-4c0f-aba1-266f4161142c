const cron = require("node-cron");
const moment = require("moment");
const DCAHistory = require("../model/DCAHistory");
const DCA = require("../model/DCA");
const { getTokenPrice } = require("./tokenPrice");
const { swapTokenUniversal } = require("./DCAEthSwap");

// Constants
const BATCH_SIZE = 10; // Process 10 DCAs at a time
const RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 5000; // 5 seconds

// Helper function to delay execution
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to handle swap execution
async function executeSwap(dca, amountToSwap) {
  const ETH_ADDRESS = "******************************************";
  const WETH_ADDRESS = "******************************************";

  const isNative = [
    dca.inputTokenAddress.toLowerCase(),
    ETH_ADDRESS.toLowerCase(),
    WETH_ADDRESS.toLowerCase()
  ].includes(dca.inputTokenAddress.toLowerCase());

  const isNativeOut = [
    dca.outputTokenAddress.toLowerCase(),
    ETH_ADDRESS.toLowerCase(),
    WETH_ADDRESS.toLowerCase()
  ].includes(dca.outputTokenAddress.toLowerCase());

  for (let attempt = 1; attempt <= RETRY_ATTEMPTS; attempt++) {
    try {
      const swapResult = await swapTokenUniversal({
        tokenInAddress: dca.inputTokenAddress,
        tokenInDecimal: dca.inputDecimals,
        tokenInSymbol: dca.allocationCurrency,
        tokenInName: dca.tokenInName,
        tokenOutAddress: dca.outputTokenAddress,
        tokenOutDecimal: dca.outPutDecimals,
        tokenOutSymbol: dca.currency,
        tokenOutName: dca.tokenOutName,
        amountIn: amountToSwap.toString(),
        slippageTolerance: 50,
        recipientAddress: dca.walletAddress,
        isNative,
        isNativeOut,
        chainID: dca.ChainId,
        privateKey: process.env.PRIVATE_KEY,
      });

      if (swapResult.error) {
        console.log(`[Swap Failed] Attempt ${attempt}/${RETRY_ATTEMPTS}: ${swapResult.details}`);
        if (attempt === RETRY_ATTEMPTS) throw new Error(swapResult.details);
        await delay(RETRY_DELAY);
        continue;
      }

      return swapResult;
    } catch (error) {
      console.error(`[Swap Error] Attempt ${attempt}/${RETRY_ATTEMPTS}:`, error);
      if (attempt === RETRY_ATTEMPTS) throw error;
      await delay(RETRY_DELAY);
    }
  }
}

// Helper function to update DCA history
async function updateDCAHistory(dca, swapResult) {
  await DCAHistory.findOneAndUpdate(
    { dcaId: dca._id },
    {
      $set: {
        dateTime: new Date(),
        transactionHash: swapResult.transactionHash,
        gasUsed: swapResult.gasUsed,
      },
      $inc: {
        inputAmount: dca.amountPerPart,
        outputAmount: parseFloat(swapResult.outputAmount),
        totalInvested: dca.amountPerPart,
        totalHoldings: parseFloat(swapResult.outputAmount),
        inputTokenAddress: inputTokenAddress,
        outputTokenAddress: outputTokenAddress,
      },
    },
    { new: true, upsert: true }
  );
}

function DCACronJob() {
  // Run every minute instead of every second
  cron.schedule("* * * * *", async () => {
    console.log("[DCA Cron] Starting execution...");
    const now = moment();

    try {
      // Get active DCAs in batches
      const activeDCAs = await DCA.find({ status: "active" }).limit(BATCH_SIZE);

      for (const dca of activeDCAs) {
        try {
          const {
            category,
            executionTimes,
            completedParts = 0,
            walletAddress,
            inputTokenAddress,
            outputTokenAddress,
            amountPerPart,
            amount,
            durationUnit,
            totalDuration,
            portfolioIncreaseAmount,
            outPutDecimals,
            inputDecimals,
            outPutSymbol,
            inputSymbol,
          } = dca;

          // Time-based DCA
          if (category === "time" && executionTimes && completedParts < executionTimes.length) {
            const nextExecutionTime = moment(executionTimes[completedParts]);

            if (nextExecutionTime.isSameOrBefore(now)) {
              console.log(`[Time-DCA] Executing for ${walletAddress}`);

              const ETH_ADDRESS = "******************************************";
              const WETH_ADDRESS = "******************************************";
              let isNative = false;
              let isNativeOut = false;

              if ([ ETH_ADDRESS.toLowerCase(), WETH_ADDRESS.toLowerCase()].includes(inputTokenAddress.toLowerCase())) {
                isNative = true;
              }

              if ([ ETH_ADDRESS.toLowerCase(), WETH_ADDRESS.toLowerCase()].includes(outputTokenAddress.toLowerCase())) {
                isNativeOut = true;
              }

              let swapResult;
              for (let attempt = 1; attempt <= RETRY_ATTEMPTS; attempt++) {
                try {
                  swapResult = await swapTokenUniversal({
                    tokenInAddress: inputTokenAddress,
                    tokenInDecimal: inputDecimals,
                    tokenInSymbol: dca.allocationCurrency,
                    tokenInName: dca.tokenInName,
                    tokenOutAddress: outputTokenAddress,
                    tokenOutDecimal: outPutDecimals,
                    tokenOutSymbol: dca.currency,
                    tokenOutName: dca.tokenOutName,
                    amountIn: amountPerPart.toString(),
                    slippageTolerance: 50,
                    recipientAddress: walletAddress,
                    isNative,
                    isNativeOut:false,
                    chainID: 1,
                    privateKey: process.env.DCAPRIVATEKEY,
                  });

                  if (swapResult.error) {
                    console.log(`[Swap Failed] Attempt ${attempt}/${RETRY_ATTEMPTS}: ${swapResult.details}`);
                    if (attempt === RETRY_ATTEMPTS) throw new Error(swapResult.details);
                    await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
                    continue;
                  }
                  break;
                } catch (error) {
                  console.error(`[Swap Error] Attempt ${attempt}/${RETRY_ATTEMPTS}:`, error);
                  if (attempt === RETRY_ATTEMPTS) throw error;
                  await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
                }
              }

              if (swapResult && !swapResult.error) {
                await DCAHistory.findOneAndUpdate(
                  { dcaId: dca._id },
                  {
                    $set: {
                      dateTime: new Date(),
                      transactionHash: swapResult.transactionHash,
                      gasUsed: swapResult.gasUsed,
                    },
                    $inc: {
                      inputAmount: amountPerPart,
                      outputAmount: parseFloat(swapResult.outputAmount),
                      totalInvested: amountPerPart,
                      totalHoldings: parseFloat(swapResult.outputAmount),
                      inputTokenAddress: inputTokenAddress,
                      outputTokenAddress: outputTokenAddress,
                    },
                  },
                  { new: true, upsert: true }
                );

                dca.completedParts += 1;
                if (dca.completedParts >= executionTimes.length) {
                  dca.status = "completed";
                  console.log(`[DCA Complete] DCA ${dca._id} marked as completed.`);
                }
                await dca.save();
              }
            }
          }

        } catch (error) {
          console.error(`[DCA Error] Failed to process DCA ${dca._id}:`, error);
          // Continue with next DCA instead of stopping the entire batch
          continue;
        }
      }
    } catch (err) {
      console.error("[DCA Cron] Fatal error:", err);
    }
  });
}

module.exports = DCACronJob;
