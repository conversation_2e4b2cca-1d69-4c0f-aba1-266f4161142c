const {
  <PERSON><PERSON><PERSON><PERSON>,
  SwapType,
  nativeOnChain,
} = require("@uniswap/smart-order-router");
const {
  Token,
  CurrencyAmount,
  TradeType,
  Percent,
  WETH9,
} = require("@uniswap/sdk-core");
const { ethers } = require("ethers"); // :white_check_mark: Correct import
// const swapTokenUniversal = async ({
//   tokenInAddress,
//   tokenInDecimal,
//   tokenInSymbol,
//   tokenInName,
//   tokenOutAddress,
//   tokenOutDecimal,
//   tokenOutSymbol,
//   tokenOutName,
//   amountIn,
//   slippageTolerance,
//   recipientAddress,
//   isNative,
//   isNativeOut,
//   privateKey,
//   chainID,
// }) => {
//   try {
//     const RPC_URL =
//       chainID === 1
//         ? "https://frequent-summer-friday.quiknode.pro/2c0b05f084892dfb225d7a21eefafdb54dd233d6" // Replace with your actual Ethereum RPC
//         : "https://billowing-quaint-sanctuary.bsc.quiknode.pro/055ed6d4e8ca39dd51f263d3c1d2fb05f96ca6bf";
//     const provider = new ethers.providers.JsonRpcProvider(RPC_URL);
//     const wallet = new ethers.Wallet(privateKey, provider);
//     const tokenIn = isNative
//       ? nativeOnChain(chainID)
//       : new Token(
//           chainID,
//           tokenInAddress,
//           tokenInDecimal,
//           tokenInSymbol,
//           tokenInName
//         );
//     const CUSTOM_WETH = {
//       1: WETH9[1],
//       56: new Token(
//         56,
//         "******************************************",
//         18,
//         "WBNB",
//         "Wrapped BNB"
//       ),
//     };
//     const tokenOut = isNativeOut
//       ? CUSTOM_WETH[chainID]
//       : new Token(
//           chainID,
//           tokenOutAddress,
//           tokenOutDecimal,
//           tokenOutSymbol,
//           tokenOutName
//         );
//     const parsedAmount = ethers.utils.parseUnits(
//       amountIn.toString(),
//       tokenInDecimal
//     );
//     const amountObj = CurrencyAmount.fromRawAmount(tokenIn, parsedAmount);
//     const router = new AlphaRouter({ chainId: chainID, provider });
//     const swapOpts = {
//       type: SwapType.UNIVERSAL_ROUTER,
//       recipient: recipientAddress,
//       slippageTolerance: new Percent(slippageTolerance, 10_000),
//       deadline: Math.floor(Date.now() / 1000 + 1800),
//     };
//     const route = await router.route(
//       amountObj,
//       tokenOut,
//       TradeType.EXACT_INPUT,
//       swapOpts
//     );
//     if (!route || !route.methodParameters) {
//       throw new Error("No valid route found");
//     }
//     const tx = {
//       to: route.methodParameters.to,
//       data: route.methodParameters.calldata,
//       value: route.methodParameters.value,
//       gasLimit: (
//         await provider.estimateGas({
//           from: wallet.address,
//           to: route.methodParameters.to,
//           data: route.methodParameters.calldata,
//           value: route.methodParameters.value,
//         })
//       ).mul(2),
//     };
//     const sentTx = await wallet.sendTransaction(tx);
//     const receipt = await sentTx.wait();
//     return {
//       transactionHash: receipt.transactionHash,
//     };
//   } catch (err) {
//     console.error("Swap failed:", err);
//     return { error: err.message };
//   }
// };


const swapTokenUniversal = async (swapParams) => {
  try {
    const {
      tokenInAddress,
      tokenInDecimal,
      tokenInSymbol,
      tokenInName,
      tokenOutAddress,
      tokenOutDecimal,
      tokenOutSymbol,
      tokenOutName,
      amountIn,
      slippageTolerance,
      recipientAddress,
      isNative,
      signature,
      permit,
      chainID,
      isNativeOut,
      privateKey,
    } = swapParams;
    console.log(tokenInAddress,
      tokenInDecimal,
      tokenInSymbol,
      tokenInName,
      tokenOutAddress,
      tokenOutDecimal,
      tokenOutSymbol,
      tokenOutName,
      amountIn,
      slippageTolerance,
      recipientAddress,
      isNative,
      signature,
      permit,
      chainID,
      isNativeOut,
      privateKey,)

    const FEE_WALLET_ADDRESS = "******************************************"; // Replace with your fee wallet
    const feePercent = 85; // 0.85% fee in basis points (85 / 10000)

    const supportedChains = [1, 56];
    if (!supportedChains.includes(Number(chainID))) {
      throw new Error(`Unsupported chain ID: ${chainID}`);
    }

    const chainConfig = {
      1: {
        rpc: "https://frequent-summer-friday.quiknode.pro/2c0b05f084892dfb225d7a21eefafdb54dd233d6",
        permit2: "******************************************",
        universalRouter: "******************************************",
      },
      56: {
        rpc: "https://billowing-quaint-sanctuary.bsc.quiknode.pro/055ed6d4e8ca39dd51f263d3c1d2fb05f96ca6bf",
        permit2: "******************************************",
        universalRouter: "******************************************",
      },
    };

    const provider = new ethers.providers.JsonRpcProvider(
      chainConfig[chainID].rpc
    );
    const wallet = new ethers.Wallet(privateKey, provider);

    const tokenIn = isNative
      ? nativeOnChain(chainID)
      : new Token(
        chainID,
        tokenInAddress,
        parseInt(tokenInDecimal),
        tokenInSymbol,
        tokenInName
      );

    const tokenOut = isNativeOut
      ? nativeOnChain(chainID)
      : new Token(
        chainID,
        tokenOutAddress,
        parseInt(tokenOutDecimal),
        tokenOutSymbol,
        tokenOutName
      );

    let parsedAmount = ethers.utils.parseUnits(
      amountIn.toString(),
      tokenInDecimal
    );
    let feeAmountInWei = ethers.BigNumber.from(0);
    let netSwapAmount = parsedAmount;

    // Deduct input fee if ETH is input
    // if (isNative) {
    //   feeAmountInWei = parsedAmount.mul(feePercent).div(10000);
    //   netSwapAmount = parsedAmount.sub(feeAmountInWei);

    //   if (feeAmountInWei.gt(0)) {
    //     const feeTx = await wallet.sendTransaction({
    //       to: FEE_WALLET_ADDRESS,
    //       value: feeAmountInWei,
    //     });
    //     await feeTx.wait();
    //   }
    // }

    const inAmount = CurrencyAmount.fromRawAmount(tokenIn, netSwapAmount);
    console.log("Parsed input amount after fee:", netSwapAmount.toString());

    const swapOpts = {
      type: SwapType.UNIVERSAL_ROUTER,
      recipient: recipientAddress,
      slippageTolerance: new Percent(slippageTolerance, 10_000),
      deadline: Math.floor(Date.now() / 1000 + 1800),
    };

    if (!isNative && permit && signature) {
      swapOpts.inputTokenPermit = {
        ...permit,
        signature,
      };
    }

    const router = new AlphaRouter({ chainId: chainID, provider });
    const route = await router.route(
      inAmount,
      tokenOut,
      TradeType.EXACT_INPUT,
      swapOpts,
      {
        maxSwapsPerPath: 2,
        protocols: ['V3'],
        forceCrossProtocol: false
      }
    );

    console.log("this is router", route)

    if (!route || !route.methodParameters) {
      throw new Error("No valid route found for the swap");
    }

    const nonce = await provider.getTransactionCount(wallet.address);

    let tx = {
      nonce: nonce,
      data: route.methodParameters.calldata,
      to: route.methodParameters.to,
      value: isNative ? netSwapAmount : route.methodParameters.value,
      from: wallet.address,

    };

    const estimatedGas = await provider.estimateGas(tx);
    const transaction = await wallet.sendTransaction(tx);
    const receipt = await transaction.wait();

    // Deduct output side fee if nativeOut is true
    // let feeOutAmount = "0";
    // let feeOut;
    // let netOutput = route.quote.toFixed(tokenOutDecimal);
    // if (isNativeOut) {
    //   const outputRaw = ethers.utils.parseUnits(
    //     route.quote.toFixed(tokenOutDecimal),
    //     tokenOutDecimal
    //   );
    //   feeOut = outputRaw.mul(feePercent).div(10000);
    //   const netOut = outputRaw.sub(feeOut);
    //   feeOutAmount = ethers.utils.formatUnits(feeOut, tokenOutDecimal);
    //   netOutput = ethers.utils.formatUnits(netOut, tokenOutDecimal);
    // }
    // if (isNativeOut && feeOut.gt(0)) {
    //   const feeTransferTx = await wallet.sendTransaction({
    //     to: FEE_WALLET_ADDRESS,
    //     value: feeOut,
    //   });
    //   await feeTransferTx.wait();
    // }

    // if (!isNative && !isNativeOut) {
    //   const outputRaw = ethers.utils.parseUnits(
    //     route.quote.toFixed(tokenOutDecimal),
    //     tokenOutDecimal
    //   );
    //   const feeAmount = outputRaw.mul(feePercent).div(10000);
    //   const netAmount = outputRaw.sub(feeAmount);

    //   const tokenOutContract = new ethers.Contract(
    //     tokenOutAddress,
    //     ["function transfer(address to, uint256 amount) public returns (bool)"],
    //     wallet
    //   );

    //   const sendFeeTx = await tokenOutContract.transfer(
    //     FEE_WALLET_ADDRESS,
    //     feeAmount
    //   );
    //   await sendFeeTx.wait();

    //   feeOutAmount = ethers.utils.formatUnits(feeAmount, tokenOutDecimal);
    //   netOutput = ethers.utils.formatUnits(netAmount, tokenOutDecimal);
    // }

    // Optional: Save swap data

    // const tokenSwapData = await TokenSwaps.create({
    //   walletAddress: wallet.address,
    //   tokenInAddress,
    //   tokenOutAddress,
    //   inputAmount: amountIn,
    //   transactionHash: receipt.transactionHash,
    // });
    // await tokenSwapData.save();

    return {
      transactionHash: receipt.transactionHash,
      inputAmount: inAmount.toExact(),
      outputAmount: netOutput,
      outputFee: isNativeOut ? feeOutAmount : "0",
      gasUsed: receipt.gasUsed.toString(),
    };
  } catch (error) {
    console.error("Swap execution error:", error);
    return {
      error: "Swap failed",
      details: error.message,
      stack: error.stack,
    };
  }
};

// Utility to transfer tokens (ETH or ERC20) from backend wallet to user
const transferToken = async ({ to, amount, tokenAddress, decimals, isNative, chainId, privateKey }) => {
  try {
    console.log("this is data of the amount ",to, amount, tokenAddress, decimals, isNative, chainId, privateKey)
    const chainConfig = {
      1: {
        rpc: "https://frequent-summer-friday.quiknode.pro/2c0b05f084892dfb225d7a21eefafdb54dd233d6",
      },
      56: {
        rpc: "https://billowing-quaint-sanctuary.bsc.quiknode.pro/055ed6d4e8ca39dd51f263d3c1d2fb05f96ca6bf",
      },
    };
    const provider = new ethers.providers.JsonRpcProvider(chainConfig[chainId].rpc);
    const wallet = new ethers.Wallet(privateKey, provider);
    let tx;
    if (isNative) {
      console.log("i am insw the native")
      // Send ETH/BNB
      const value = ethers.utils.parseUnits(amount.toString(), decimals);
      const balance = await wallet.getBalance();
      console.log(`Native balance: ${ethers.utils.formatUnits(balance, decimals)}`);
      if (balance.lt(value)) {
        throw new Error("Insufficient ETH/BNB balance");
      }
      tx = await wallet.sendTransaction({
        to,
        value,
        gasLimit: 21000, // Manual gas limit for native transfer
      });
    } else {
      // Send ERC20
      const abi = [
        "function balanceOf(address owner) view returns (uint256)",
        "function transfer(address to, uint256 amount) public returns (bool)"
      ];
      const contract = new ethers.Contract(tokenAddress, abi, wallet);
      const value = ethers.utils.parseUnits(amount.toString(), decimals);
      const tokenBalance = await contract.balanceOf(wallet.address);
      console.log(`Token balance: ${ethers.utils.formatUnits(tokenBalance, decimals)}`);
      if (tokenBalance.lt(value)) {
        throw new Error("Insufficient token balance");
      }
      tx = await contract.transfer(to, value, { gasLimit: 100000 }); // Manual gas limit for ERC20
    }
    const receipt = await tx.wait();
    return { transactionHash: receipt.transactionHash };
  } catch (error) {
    console.error("Token transfer error:", error);
    return { error: error.message };
  }
};

module.exports = {
  swapTokenUniversal,
  transferToken,
};
