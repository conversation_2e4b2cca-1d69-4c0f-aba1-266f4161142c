const express = require("express");
const router = express.Router();
const insuranceController = require("../controller/insurance");
const validator = require("../middleware/validateTokenHandler");

router.post("/create-Insurance", insuranceController.createInsurance);
router.get("/get-Insurance", insuranceController.getInsurance);
router.post("/update-payment-status", insuranceController.updatePaymentStatus);
router.get("/get-insurance/:insuranceId", insuranceController.getInsuranceById);
router.get(
  "/dasboardInsuredUsers",
  insuranceController.getDashboardInsuredUsers
);
router.get(
  "/get-insured-users",
  validator,
  insuranceController.getInsuredUsers
);
router.get(
  "/get-insurance-history/:walletAddress",
  insuranceController.getInsuranceHistory
);
router.get("/get-fee-percentage", insuranceController.getFeePercentage);
router.post(
  "/calculte-fee-percentage",
  insuranceController.feePercentageOnAmount
);
router.get("/get-env-keys", insuranceController.getReturnEnvKeys);
router.get(
  "/get-users-transactions-details",
  validator,
  insuranceController.getUsersTransactionsDetails
);
router.get("/get-transaction-details/:walletAddress", validator, insuranceController.getInsuranceByWalletAddress);
router.get("/get-payment-tracking", validator, insuranceController.usersPaymentTracking);
router.get("/get-payment-track/:id", validator, insuranceController.getPaymentTackById);
router.get("/get-insured-user/:walletAddress", insuranceController.getInsuredUsersByWalletAddress);

module.exports = router;
