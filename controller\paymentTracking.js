const axios = require("axios");
const { BuyTransaction, SellTransaction } = require("../model/buyTranscation");
const { Connection, PublicKey } = require("@solana/web3.js");

const connection = new Connection(
  process.env.RPC_URL || "https://api.mainnet-beta.solana.com"
);

const tokenNameCache = new Map();
exports.getTokenName = async (mint) => {
  if (tokenNameCache.has(mint)) return tokenNameCache.get(mint);
  if (mint === "So11111111111111111111111111111111111111112") {
    tokenNameCache.set(mint, "SOL");
    return "SOL";
  }
  const headers = { "X-API-Key": process.env.MORALIS_API_KEY };
  try {
    const res = await axios.get(
      `https://solana-gateway.moralis.io/token/mainnet/${mint}/metadata`,
      { headers }
    );
    console.log(res.data);  
    const decimals = res.data?.decimals;
    if (decimals) {
      tokenNameCache.set(mint, `${symbol} (${decimals} decimals)`);
      return `${symbol} (${decimals} decimals)`;
    }
    const symbol =
      res.data?.symbol || `${mint.slice(0, 4)}...${mint.slice(-4)}`;
    tokenNameCache.set(mint, symbol);
    return symbol;
    
  } catch { }
  const short = `${mint.slice(0, 4)}...${mint.slice(-4)}`;
  tokenNameCache.set(mint, short);
  return short;
}

function formatAmount(amount) {
  return Number(amount.toFixed(9));
}

async function getExchangeName(txDataString) {
  const knownExchanges = {
    JUP: "Jupiter",
    JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4: "Jupiter",
    JUP4Fb2cqiRUcaTHdrPC8h2gNsA2ETXiPDD33WcGuJB: "Jupiter",
    srmqPvymJeFKQ4zGQed1GFppgkRHL9kaELCbyksJtPX: "Serum",
    RAYGNmFqYRxH8qDpQpfpZUy9dZPPfFwGMPcvFbEFzLs: "Raydium",
    orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE: "Orca",
    whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc: "Orca",
  };
  for (const [programId, exchangeName] of Object.entries(knownExchanges)) {
    if (txDataString.includes(programId)) {
      return exchangeName;
    }
  }
  return "Unknown Exchange";
}

async function getMoralisSwapsMap(walletAddress) {
  const headers = { "X-API-Key": process.env.MORALIS_API_KEY };
  const url = `https://solana-gateway.moralis.io/account/mainnet/${walletAddress}/swaps`;
  const swapMap = new Map();
  try {
    let cursor = null;
    let hasMore = true;
    while (hasMore) {
      const queryUrl = cursor ? `${url}?cursor=${cursor}` : url;
      const res = await axios.get(queryUrl, { headers });
      const results = res.data?.result || [];
      results.forEach((swap) => {
        swapMap.set(swap.transactionHash, swap);
      });
      cursor = res.data.cursor;
      hasMore = !!cursor;
    }
  } catch (err) {
    console.error(
      "Failed to fetch swaps from Moralis:",
      err?.response?.data || err.message
    );
  }
  return swapMap;
}

exports.paymentTracking = async (req, res) => {
  try {
    const walletAddress = req.params.wallet;
    let {
      limit,
      sendMinAmount,
      sendMaxAmount,
      receiveMinAmount,
      receiveMaxAmount,
      token,
      dateFrom,
      dateTo,
      inputToken,
      outputToken,
      inputMinAmount,
      inputMaxAmount,
      outputMinAmount,
      outputMaxAmount,
      // OnRamp filters
      minAmount, maxAmount, minReceived, maxReceived, boughtCurrency, fiatCurrency,
      // OffRamp filters
      sellMinAmount, sellMaxAmount, soldMin, soldMax, soldCurrency, soldFiat
    } = req.query;

    minAmount = minAmount ? parseFloat(minAmount) : null;
    maxAmount = maxAmount ? parseFloat(maxAmount) : null;
    minReceived = minReceived ? parseFloat(minReceived) : null;
    maxReceived = maxReceived ? parseFloat(maxReceived) : null;

    sellMinAmount = sellMinAmount ? parseFloat(sellMinAmount) : null;
    sellMaxAmount = sellMaxAmount ? parseFloat(sellMaxAmount) : null;
    soldMin = soldMin ? parseFloat(soldMin) : null;
    soldMax = soldMax ? parseFloat(soldMax) : null;

    const fromDate = dateFrom ? new Date(dateFrom) : null;
    const toDate = dateTo ? new Date(dateTo) : null;

    // Parse query parameters
    limit = parseInt(limit) || 10;
    sendMinAmount = sendMinAmount ? parseFloat(sendMinAmount) : null;
    sendMaxAmount = sendMaxAmount ? parseFloat(sendMaxAmount) : null;
    receiveMinAmount = receiveMinAmount ? parseFloat(receiveMinAmount) : null;
    receiveMaxAmount = receiveMaxAmount ? parseFloat(receiveMaxAmount) : null;

    const fromTimestamp = dateFrom ? new Date(dateFrom).getTime() / 1000 : null;
    const toTimestamp = dateTo ? new Date(dateTo).getTime() / 1000 : null;

    inputMinAmount = inputMinAmount ? parseFloat(inputMinAmount) : null;
    inputMaxAmount = inputMaxAmount ? parseFloat(inputMaxAmount) : null;
    outputMinAmount = outputMinAmount ? parseFloat(outputMinAmount) : null;
    outputMaxAmount = outputMaxAmount ? parseFloat(outputMaxAmount) : null;

    const normalizedToken = token ? token.toUpperCase() : null;
    const normalizedInputToken = inputToken ? inputToken.toUpperCase() : null;
    const normalizedOutputToken = outputToken ? outputToken.toUpperCase() : null;

    if (!walletAddress) {
      return res.status(400).json({ error: "Wallet address is required" });
    }

    //onramp
    const buyFilter = { walletAddress };

    if (minAmount !== null || maxAmount !== null) {
      buyFilter.amount = {};
      if (minAmount !== null) buyFilter.amount.$gte = minAmount;
      if (maxAmount !== null) buyFilter.amount.$lte = maxAmount;
    }

    if (minReceived !== null || maxReceived !== null) {
      buyFilter.amountReceived = {};
      if (minReceived !== null) buyFilter.amountReceived.$gte = minReceived;
      if (maxReceived !== null) buyFilter.amountReceived.$lte = maxReceived;
    }

    if (boughtCurrency) {
      buyFilter.currencyBought = boughtCurrency.toUpperCase();
    }

    if (fiatCurrency) {
      buyFilter.fiatCurrency = fiatCurrency.toUpperCase();
    }

    if (fromDate || toDate) {
      buyFilter.createdAt = {};
      if (fromDate) buyFilter.createdAt.$gte = fromDate;
      if (toDate) buyFilter.createdAt.$lte = toDate;
    }

    //off ramp
    const sellFilter = { walletAddress };

    if (sellMinAmount !== null || sellMaxAmount !== null) {
      sellFilter.amount = {};
      if (sellMinAmount !== null) sellFilter.amount.$gte = sellMinAmount;
      if (sellMaxAmount !== null) sellFilter.amount.$lte = sellMaxAmount;
    }

    if (soldMin !== null || soldMax !== null) {
      sellFilter.amountSold = {};
      if (soldMin !== null) sellFilter.amountSold.$gte = soldMin;
      if (soldMax !== null) sellFilter.amountSold.$lte = soldMax;
    }

    if (soldCurrency) {
      sellFilter.currencySold = soldCurrency.toUpperCase();
    }

    if (soldFiat) {
      sellFilter.fiatCurrency = soldFiat.toUpperCase();
    }

    if (fromDate || toDate) {
      sellFilter.createdAt = {};
      if (fromDate) sellFilter.createdAt.$gte = fromDate;
      if (toDate) sellFilter.createdAt.$lte = toDate;
    }
    const buyTransactions = await BuyTransaction.find(buyFilter).sort({ createdAt: -1 });
    const sellTransactions = await SellTransaction.find(sellFilter).sort({ createdAt: -1 });

    const isEthereum =
      walletAddress.startsWith("0x") && walletAddress.length === 42;

    if (isEthereum) {
      // ---------- Ethereum Handling via Etherscan ----------
      const url = `https://api.etherscan.io/api?module=account&action=txlist&address=${walletAddress}&startblock=0&endblock=********&page=1&offset=${limit}&sort=desc&apikey=${process.env.ETHERSCAN_API_KEY}`;
      const response = await axios.get(url);
      const transactions = response.data?.result || [];

      let parsedTxs = transactions.map((tx) => ({
        Category:
          tx.from.toLowerCase() === walletAddress.toLowerCase()
            ? "Send"
            : "Receive",
        TokenName: "ETH",
        MintAddress: "******************************************",
        Amount: formatAmount(parseFloat(tx.value) / 1e18),
        TransactionHash: tx.hash,
        Timestamp: parseInt(tx.timeStamp),
        From: tx.from,
        To: tx.to,
        PreAmount: null,
        PostAmount: null,
        gayaTransaction: false,
      }));

      // Apply filters to Ethereum transactions
      parsedTxs = applyFilters(parsedTxs, {
        sendMinAmount,
        sendMaxAmount,
        receiveMinAmount,
        receiveMaxAmount,
        normalizedToken,
        fromTimestamp,
        toTimestamp,
      });

      return res.json({
        AllTransactionsParsed: parsedTxs,
        GayaTransactionsParsed: [],
        AllSwaps: [],
        GayaSwaps: [],
        OnRampTransactions: buyTransactions,
        OffRampTransactions: sellTransactions,
      });
    }

    // ---------- Solana Handling ----------
    const publicKey = new PublicKey(walletAddress);
    const transactions = await connection.getSignaturesForAddress(publicKey, {
      limit,
    });

    console.log("📦 Fetched transactions:", transactions.length);

    const moralisSwapMap = await getMoralisSwapsMap(walletAddress);
    const allTransactionsParsed = [];
    const gayaTransactionsParsed = [];
    const allSwaps = [];
    const gayaSwaps = [];
    const batchSize = 10;

    for (let i = 0; i < transactions.length; i += batchSize) {
      const batch = transactions.slice(i, i + batchSize);
      const batchPromises = batch.map(async (tx) => {
        const txHash = tx.signature;
        let txInfo;
        try {
          txInfo = await connection.getParsedTransaction(txHash, {
            maxSupportedTransactionVersion: 0,
          });
        } catch {
          return null;
        }
        if (!txInfo || !txInfo.meta) return null;

        const txDataString = JSON.stringify(txInfo);
        const isGayaRelated =
          txInfo.meta.logMessages?.some((log) =>
            log.toLowerCase().includes("gaya")
          ) || false;

        let input = null;
        let output = null;
        let isPartOfSwap = false;
        let intermediateMints = [];

        if (
          txInfo.transaction?.message?.instructions &&
          txInfo.meta?.preTokenBalances &&
          txInfo.meta?.postTokenBalances
        ) {
          const tokenChanges = new Map();

          for (const balance of txInfo.meta.preTokenBalances) {
            const amount = parseFloat(
              balance.uiTokenAmount.uiAmountString || "0"
            );
            tokenChanges.set(balance.mint, {
              mint: balance.mint,
              pre: amount,
              post: amount,
              diff: 0,
            });
          }

          for (const balance of txInfo.meta.postTokenBalances) {
            const amount = parseFloat(
              balance.uiTokenAmount.uiAmountString || "0"
            );
            const existing = tokenChanges.get(balance.mint) || {
              mint: balance.mint,
              pre: 0,
            };
            existing.post = amount;
            existing.diff = amount - existing.pre;
            tokenChanges.set(balance.mint, existing);
          }

          const diffs = Array.from(tokenChanges.values())
            .filter((t) => Math.abs(t.diff) > 0.000001)
            .sort((a, b) => Math.abs(b.diff) - Math.abs(a.diff));

          const inputs = diffs.filter((d) => d.diff < 0);
          const outputs = diffs.filter((d) => d.diff > 0);

          if (inputs.length && outputs.length) {
            input = inputs[0];
            output = outputs[outputs.length - 1];
            isPartOfSwap = true;
            intermediateMints = diffs
              .filter((d) => d.mint !== input.mint && d.mint !== output.mint)
              .map((d) => d.mint);

            let moralisDetails = {
              InputToken: await getTokenName(input.mint),
              OutputToken: await getTokenName(output.mint),
              Exchange: await getExchangeName(txDataString),
            };

            const matchedSwap = moralisSwapMap.get(txHash);
            if (matchedSwap) {
              moralisDetails = {
                InputToken: matchedSwap.sold.name || moralisDetails.InputToken,
                OutputToken:
                  matchedSwap.bought.name || moralisDetails.OutputToken,
                Exchange: matchedSwap.exchangeName || moralisDetails.Exchange,
                TransactionType: matchedSwap.transactionType,
                TotalValueUSD: matchedSwap.totalValueUsd,
              };
            }

            const consolidatedSwap = {
              ...moralisDetails,
              InputAmount: Math.abs(input.diff),
              OutputAmount: output.diff,
              TransactionHash: txHash,
              Timestamp: txInfo.blockTime,
              gayaTransaction: isGayaRelated,
              isConsolidated: true,
              hopCount: diffs.length,
              intermediateTokens: await Promise.all(
                intermediateMints.map(getTokenName)
              ),
              MintIn: input.mint,
              MintOut: output.mint,
            };

            allSwaps.push(consolidatedSwap);
            if (isGayaRelated) gayaSwaps.push(consolidatedSwap);
          }
        }

        if (!isPartOfSwap) {
          const seenMints = new Set();
          if (txInfo.meta?.preTokenBalances && txInfo.meta?.postTokenBalances) {
            for (let j = 0; j < txInfo.meta.postTokenBalances.length; j++) {
              const postBalance = txInfo.meta.postTokenBalances[j];
              const preBalance = txInfo.meta.preTokenBalances.find(
                (pre) => pre.accountIndex === postBalance.accountIndex
              );
              if (!preBalance || !postBalance) continue;

              const mintAddress = postBalance.mint;
              if (seenMints.has(mintAddress)) continue;
              seenMints.add(mintAddress);

              const tokenName = await getTokenName(mintAddress);
              const preAmount = parseFloat(
                preBalance.uiTokenAmount.uiAmount || 0
              );
              const postAmount = parseFloat(
                postBalance.uiTokenAmount.uiAmount || 0
              );
              const amount = Math.abs(postAmount - preAmount);
              if (amount === 0) continue;

              const transaction = {
                Category: postAmount > preAmount ? "Receive" : "Send",
                TokenName: tokenName,
                MintAddress: mintAddress,
                Amount: amount,
                TransactionHash: txHash,
                Timestamp: txInfo.blockTime,
                PreAmount: preAmount,
                PostAmount: postAmount,
                gayaTransaction: isGayaRelated,
              };

              allTransactionsParsed.push(transaction);
              if (isGayaRelated) gayaTransactionsParsed.push(transaction);
            }
          }

          if (txInfo.meta?.preBalances && txInfo.meta?.postBalances) {
            const accountKeys = txInfo.transaction?.message?.accountKeys || [];
            const senderIndex = accountKeys.findIndex(
              (k) => k.pubkey.toBase58() === walletAddress
            );

            if (senderIndex >= 0) {
              const preLamports = txInfo.meta.preBalances[senderIndex];
              const postLamports = txInfo.meta.postBalances[senderIndex];
              const delta = postLamports - preLamports;

              if (Math.abs(delta) > 0) {
                const amountSol = Math.abs(delta) / 1e9;
                const transaction = {
                  Category: delta > 0 ? "Receive" : "Send",
                  TokenName: "SOL",
                  MintAddress: "So11111111111111111111111111111111111111112",
                  Amount: formatAmount(amountSol),
                  TransactionHash: txHash,
                  Timestamp: txInfo.blockTime,
                  PreAmount: preLamports / 1e9,
                  PostAmount: postLamports / 1e9,
                  gayaTransaction: isGayaRelated,
                };

                allTransactionsParsed.push(transaction);
                if (isGayaRelated) gayaTransactionsParsed.push(transaction);
              }
            }
          }
        }
      });
      await Promise.all(batchPromises);
    }

    // Apply filters to all transactions
    const filteredAllTransactions = applyFilters(allTransactionsParsed, {
      sendMinAmount,
      sendMaxAmount,
      receiveMinAmount,
      receiveMaxAmount,
      normalizedToken,
      fromTimestamp,
      toTimestamp,
    });

    // Apply filters to Gaya transactions
    const filteredGayaTransactions = applyFilters(gayaTransactionsParsed, {
      inputMinAmount,
      inputMaxAmount,
      outputMinAmount,
      outputMaxAmount,
      normalizedToken,
      fromTimestamp,
      toTimestamp,
    });

    const filteredAllSwaps = allSwaps.filter((swap) => {
      if (fromTimestamp && swap.Timestamp < fromTimestamp) return false;
      if (toTimestamp && swap.Timestamp > toTimestamp) return false;

      if (normalizedToken) {
        const inputMatches = swap.InputToken.toUpperCase() === normalizedToken;
        const outputMatches = swap.OutputToken.toUpperCase() === normalizedToken;
        if (!inputMatches && !outputMatches) return false;
      }

      if (normalizedInputToken && swap.InputToken.toUpperCase() !== normalizedInputToken) return false;
      if (normalizedOutputToken && swap.OutputToken.toUpperCase() !== normalizedOutputToken) return false;

      if (inputMinAmount !== null && swap.InputAmount < inputMinAmount) return false;
      if (inputMaxAmount !== null && swap.InputAmount > inputMaxAmount) return false;

      if (outputMinAmount !== null && swap.OutputAmount < outputMinAmount) return false;
      if (outputMaxAmount !== null && swap.OutputAmount > outputMaxAmount) return false;

      return true;
    });

    const filteredGayaSwaps = gayaSwaps.filter((swap) => {
      if (fromTimestamp && swap.Timestamp < fromTimestamp) return false;
      if (toTimestamp && swap.Timestamp > toTimestamp) return false;

      if (normalizedToken) {
        const inputMatches = swap.InputToken.toUpperCase() === normalizedToken;
        const outputMatches =
          swap.OutputToken.toUpperCase() === normalizedToken;
        if (!inputMatches && !outputMatches) return false;
      }

      if (normalizedInputToken && swap.InputToken.toUpperCase() !== normalizedInputToken) return false;
      if (normalizedOutputToken && swap.OutputToken.toUpperCase() !== normalizedOutputToken) return false;

      if (inputMinAmount !== null && swap.InputAmount < inputMinAmount) return false;
      if (inputMaxAmount !== null && swap.InputAmount > inputMaxAmount) return false;

      if (outputMinAmount !== null && swap.OutputAmount < outputMinAmount) return false;
      if (outputMaxAmount !== null && swap.OutputAmount > outputMaxAmount) return false;

      return true;
    });

    return res.json({
      AllTransactionsParsed: filteredAllTransactions,
      GayaTransactionsParsed: filteredGayaTransactions,
      AllSwaps: filteredAllSwaps,
      GayaSwaps: filteredGayaSwaps,
      OnRampTransactions: buyTransactions,
      OffRampTransactions: sellTransactions,
    });
  } catch (error) {
    console.error("Error in tracking controller:", error);
    return res.status(500).json({
      error: "Internal server error",
      details: error.message,
    });
  }
};

function applyFilters(transactions, filters) {
  const {
    sendMinAmount,
    sendMaxAmount,
    receiveMinAmount,
    receiveMaxAmount,
    normalizedToken,
    fromTimestamp,
    toTimestamp,
    normalizedInputToken,
    normalizedOutputToken,
    inputMinAmount,
    inputMaxAmount,
    outputMinAmount,
    outputMaxAmount,
  } = filters;

  return transactions.filter((tx) => {
    if (tx.Category === "Send") {
      if (sendMinAmount !== null && tx.Amount < sendMinAmount) return false;
      if (sendMaxAmount !== null && tx.Amount > sendMaxAmount) return false;
    } else if (tx.Category === "Receive") {
      if (receiveMinAmount !== null && tx.Amount < receiveMinAmount)
        return false;
      if (receiveMaxAmount !== null && tx.Amount > receiveMaxAmount)
        return false;
    }

    if (normalizedToken && tx.TokenName.toUpperCase() !== normalizedToken) {
      return false;
    }

    if (inputMinAmount !== null && tx.InputAmount < inputMinAmount) return false;
    if (inputMaxAmount !== null && tx.InputAmount > inputMaxAmount) return false;

    if (outputMinAmount !== null && tx.OutputAmount < outputMinAmount) return false;
    if (outputMaxAmount !== null && tx.OutputAmount > outputMaxAmount) return false;

    if (fromTimestamp && tx.Timestamp < fromTimestamp) return false;
    if (toTimestamp && tx.Timestamp > toTimestamp) return false;

    // Input token match (for swaps)
    if (normalizedInputToken && tx.InputToken?.toUpperCase() !== normalizedInputToken) {
      return false;
    }

    // Output token match (for swaps)
    if (normalizedOutputToken && tx.OutputToken?.toUpperCase() !== normalizedOutputToken) {
      return false;
    }

    return true;
  });
}

