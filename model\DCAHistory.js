const mongoose = require("mongoose");

const DCAHistorySchema = new mongoose.Schema(
  {
    dcaId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "DCA",
    },
    walletAddress: String,
    asset: String,
    dateTime: Date,
    inputAmount: Number,
    pricePerCoin: Number,
    outputAmount: Number,
    transactionHash: String,
    gasUsed: String,
    chainId: Number,
    platform: String,
    totalInvested: Number,
    totalHoldings: Number,
    inputTokenAddress: String,
    outputTokenAddress: String,
  },
  { timestamps: true }
);

module.exports = mongoose.model("DCAHistory", DCAHistorySchema);
