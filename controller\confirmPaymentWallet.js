const express = require("express");
const asyncHandler = require("express-async-handler");
const ConfirmPaymentWallet = require("../model/confirmPaymentWallet");

const confirmPaymentWalletAddress = asyncHandler(async (req, res) => {
  const { confirmPaymentWallet } = req.body;

  if (!confirmPaymentWallet) {
    return res.status(400).json({ message: "Wallet address is required" });
  }

  const existingWalletAddress = await ConfirmPaymentWallet.findOne({
    confirmPaymentWallet,
  });

  if (existingWalletAddress) {
    return res.status(400).json({ message: "Wallet address already exists" });
  }

  const newWalletAddress = new ConfirmPaymentWallet({ confirmPaymentWallet });
  await newWalletAddress.save();

  return res.status(200).json({
    message: "Confirm payment wallet address saved",
    paymentAmount: newWalletAddress.confirmPaymentWallet,
  });
});

module.exports = { confirmPaymentWalletAddress };
