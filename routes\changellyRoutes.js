const express = require("express");
const mainController = require("../controller/changelly");

module.exports = (app) => {
  app.route("/changelly-fiat/providers-list").get(mainController.getproviders);
  app
    .route("/changelly-fiat/currency-list")
    .get(mainController.getCureencyList);
  app
    .route("/changelly-fiat/country-availability-list")
    .get(mainController.getAvalibleCountry);
  app.route("/changelly-fiat/get-offers").post(mainController.getOffers);
  app
    .route("/changelly-fiat/validate-walllet-address")
    .post(mainController.validateWalletAddress);
  app.route("/changelly-fiat/create-order").post(mainController.createOrder);
  app
    .route("/changelly-fiat/create-order-v2")
    .post(mainController.createOrderNew);
  app
    .route("/changelly-fiat/create-order-v3")
    .post(mainController.createOrderPc);

  app.route("/changelly-fiat/getOffers").post(mainController.getOffers);
  app.route("/changelly-fiat/sell-order").post(mainController.sellOrder);
  app.route("/changelly-fiat/sell-order-v2").post(mainController.sellOrderNew);
  app.route("/changelly-fiat/sell-order-v3").post(mainController.sellOrderPc);
  app
    .route("/changelly-fiat/currency-list-network")
    .get(mainController.currencyListByNetwork);

  // Fix these webhook routes
  app
    .route("/changelly-fiat/register-webhook")
    .post(mainController.registerWebhook);
  app.route("/changelly-fiat/webhook").post(mainController.handleWebhook);
  app
    .route("/changelly-fiat/get-order-status/:externalOrderId")
    .get(mainController.getOrderStatus);
};
