const Wallet = require("../model/wallet");

const axios = require("axios");
const redisClient = require("../services/redisClient");

const API_KEY = "0b3e130c-c19e-4271-b53a-03f1a447cde5";

const fetchRecentTransactions = async (
  walletAddress,
  beforeSignature = null,
  limit = 50
) => {
  let url = `https://api.helius.xyz/v0/addresses/${walletAddress}/transactions?api-key=${API_KEY}`;
  if (beforeSignature) {
    url += `&before=${beforeSignature}`;
  }

  const response = await axios.get(url);
  return response.data;
};

const fetchAllTransactions = async (walletAddress) => {
  let lastSignature = null;
  let allTransactions = [];
  while (true) {
    const batch = await fetchRecentTransactions(walletAddress, lastSignature);
    if (!batch.length) break;

    allTransactions.push(...batch);
    lastSignature = batch[batch.length - 1].signature;
  }
  return allTransactions;
};

const getTransactions = async (walletAddress) => {
  const cacheKey = `wallet:${walletAddress}:transactions`;

  const cached = await redisClient.get(cacheKey);

  if (!cached) {
    console.log("No cache found, fetching all transactions...");
    const allTransactions = await fetchAllTransactions(walletAddress);
    await redisClient.set(cacheKey, JSON.stringify(allTransactions), {
      EX: 60 * 10, // 10 minutes
    });
    return allTransactions;
  }

  console.log("Cache found, checking for new transactions...");
  const cachedTransactions = JSON.parse(cached);
  const latestCachedSignature = cachedTransactions[0]?.signature;

  const recentTransactions = await fetchRecentTransactions(walletAddress);

  const newTransactions = [];
  for (const tx of recentTransactions) {
    if (tx.signature === latestCachedSignature) break;
    newTransactions.push(tx);
  }

  if (newTransactions.length) {
    console.log(
      `Found ${newTransactions.length} new transactions, updating cache...`
    );
    const updatedCache = [...newTransactions, ...cachedTransactions];
    await redisClient.set(cacheKey, JSON.stringify(updatedCache), {
      EX: 60 * 10,
    });
    return updatedCache;
  }

  console.log("No new transactions found.");
  return cachedTransactions;
};

//post wallet address
exports.postWalletAddress = async (req, res) => {
  try {
    const { ethWalletAddress, solWalletAddress } = req.body;
    const wallet = Wallet.create({ ethWalletAddress, solWalletAddress });
    res.status(201).json(wallet);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

//get all wallet address
exports.getWalletAddress = async (req, res) => {
  try {
    const wallets = await Wallet.find();
    if (!wallets) {
      return res.status(200).json({ message: "No wallets found" });
    }
    res.status(200).json(wallets);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
exports.getTrnascationHistoryByWalletAddress = async (req, res) => {
  try {
    const { walletAddress } = req.params;
    const transactions = await getTransactions(walletAddress);
    res.status(200).json(transactions);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};
