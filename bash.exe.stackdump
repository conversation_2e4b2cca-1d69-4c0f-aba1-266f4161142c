Stack trace:
Frame         Function      Args
0007FFFFAC00  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFAC00, 0007FFFF9B00) msys-2.0.dll+0x1FE8E
0007FFFFAC00  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAED8) msys-2.0.dll+0x67F9
0007FFFFAC00  000210046832 (000210286019, 0007FFFFAAB8, 0007FFFFAC00, 000000000000) msys-2.0.dll+0x6832
0007FFFFAC00  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFAC00  000210068E24 (0007FFFFAC10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAEE0  00021006A225 (0007FFFFAC10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF86F720000 ntdll.dll
7FF86DF80000 KERNEL32.DLL
7FF86CBB0000 KERNELBASE.dll
7FF86DDB0000 USER32.dll
7FF86CA00000 win32u.dll
7FF86DC40000 GDI32.dll
7FF86D190000 gdi32full.dll
7FF86D0E0000 msvcp_win.dll
7FF86C8B0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF86F620000 advapi32.dll
7FF86DC70000 msvcrt.dll
7FF86DA90000 sechost.dll
7FF86D970000 RPCRT4.dll
7FF86BED0000 CRYPTBASE.DLL
7FF86CF80000 bcryptPrimitives.dll
7FF86E160000 IMM32.DLL
