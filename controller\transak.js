const asyncHandler = require("express-async-handler");
const axios = require("axios");
let moonPay;
(async () => {
  const { MoonPay } = await import("@moonpay/moonpay-node");
  moonPay = new MoonPay("****************************************");
})();

//const TRANSAK_BASE_URL = "https://staging-global.transak.com";
// const createTransaction = asyncHandler(async (req, res) => {
//   try {
//     const {
//       network,
//       walletAddress,
//       fiatAmount,
//       defaultFiatCurrency,
//       cryptoCurrencyCode,
//       environment = "STAGING",
//       isTestnet = "true",
//     } = req.query;

//     const queryParams = new URLSearchParams({
//       apiKey: process.env.API_KEY,
//       fiatAmount,
//       defaultFiatCurrency,
//       network,
//       walletAddress,
//       disableWalletAddressForm: "true",
//       cryptoCurrencyCode,
//       environment,
//       isTestnet,
//     });
//     const transakUrl = `${TRANSAK_BASE_URL}?${queryParams.toString()}`;
//     res.json({ transakUrl });
//   } catch (error) {
//     console.error("Error generating Transak URL:", error);
//     res.status(500).json({ message: "Server Error" });
//   }
// });

const initiatePayment = asyncHandler(async (req, res) => {
  // Make sure moonPay is initialized
  if (!moonPay) {
    const { MoonPay } = await import("@moonpay/moonpay-node");
    moonPay = new MoonPay("****************************************");
  }

  const { walletAddress, fiatAmount, defaultFiatCurrency, cryptoCurrencyCode } =
    req.query;
  // const walletAddress = "455WymxmGBMTH93E2SLGaYfjCZvnPP5LMTG4XRaCLbYL";
  // const amount = 100;
  // const currency = "usd";
  // const cryptoType = "usdc";
  const params = {
    apiKey: "pk_live_V8N3d6NFEHJp16mPVoi54AY41VcD1hi",
    // currencyCode: cryptoType,
    walletAddress,
    baseCurrencyAmount: fiatAmount.toString(),
    baseCurrencyCode: defaultFiatCurrency,
    currencyCode: cryptoCurrencyCode,
    defaultCurrencyCode: cryptoCurrencyCode,
    showWalletAddressForm: "false",
    theme: "dark",
    showAllCurrencies: "true",
  };

  const url = moonPay.url.generate({ flow: "buy", params });
  return res.status(200).json({ success: true, checkoutUrl: url });
});

const initiateSELL = asyncHandler(async (req, res) => {
  if (!moonPay) {
    const { MoonPay } = await import("@moonpay/moonpay-node");
    moonPay = new MoonPay("****************************************");
  }
  const { walletAddress, amount, currency, cryptoType } = req.query;
  const params = {
    apiKey: "pk_live_V8N3d6NFEHJp16mPVoi54AY41VcD1hi",
    walletAddress,
    baseCurrencyAmount: amount.toString(),
    baseCurrencyCode: cryptoType,
    quoteCurrencyCode: currency,
    showAllCurrencies: "true",
    theme: "dark",
  };
  const url = moonPay.url.generate({ flow: "sell", params });
  return res.status(200).json({ success: true, checkoutUrl: url });
});

const getMoonPayQuote = asyncHandler(async (req, res) => {
  const { currencyCode, baseCurrencyCode } = req.query;
  if (!currencyCode || !baseCurrencyCode) {
    return res
      .status(400)
      .json({ error: "currencyCode and baseCurrencyCode are required" });
  }
  try {
    const response = await axios.get(
      `https://api.moonpay.com/v3/currencies/${currencyCode}/limits`,
      {
        params: {
          baseCurrencyCode: baseCurrencyCode,
          apiKey: "pk_live_V8N3d6NFEHJp16mPVoi54AY41VcD1hi",
        },
      }
    );
    res.json(response.data);
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ error: "An error occurred while fetching the data" });
  }
});

const getFiatCurrencies = asyncHandler(async (req, res) => {
  try {
    axios
      .get(
        `
https://api-stg.transak.com/api/v2/currencies/fiat-currencies`
      )
      .then((currencies) => {
        return res.status(200).json(currencies.data.response);
      });
  } catch (error) {
    console.error("Error fetching currencies:", error);
    res.status(500).json({ message: "Server Error" });
  }
});

const getCryptoCurrencies = asyncHandler(async (req, res) => {
  try {
    const response = await axios.get(
      "https://api-stg.transak.com/api/v2/currencies/crypto-currencies"
    );

    // Filter only Solana cryptocurrencies
    const solanaCurrencies = response.data.response.filter(
      (crypto) =>
        crypto.network.name === "solana" || crypto.network.name === "SOLANA"
    );

    res.json({ response: solanaCurrencies });
  } catch (error) {
    console.error("Error fetching fiat currencies:", error);
    res.status(500).json({ message: "Server Error" });
  }
});

const createCryptoToFiatTransaction = asyncHandler(async (req, res) => {
  try {
    const { cryptoAmount } = req.query;
    if (!cryptoAmount) {
      return res.status(400).json({ message: "Crypto amount is required" });
    }

    if (cryptoAmount < 0.03188645) {
      return res
        .status(400)
        .json({ message: "Sol amount cannot be less than 0.03188645" });
    }

    if (cryptoAmount > 166.74623833) {
      return res
        .status(400)
        .json({ message: "Sol amount cannot be greater than 166.74623833" });
    }

    const transakUrl = `https://global.transak.com/?apiKey=02624956-010b-4775-8e31-7b9c8b82df76&cryptoCurrencyCode=SOL&network=solana&defaultCryptoAmount=${cryptoAmount}&productsAvailed=SELL`;

    res.json({ transakUrl });
  } catch (error) {
    console.error("Error generating Transak URL for crypto-to-fiat:", error);
    res.status(500).json({ message: "Server Error" });
  }
});

module.exports = {
  initiatePayment,
  initiateSELL,
  getFiatCurrencies,
  getCryptoCurrencies,
  createCryptoToFiatTransaction,
  getMoonPayQuote,
};
