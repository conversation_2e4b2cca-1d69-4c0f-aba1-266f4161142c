const {
  uploadToGCS,
  deleteFromGCS,
  generateUniqueFileName,
} = require("./google-cloud-storage");

const uploadFile = async (file, folder = "", options = {}) => {
  try {
    if (!file?.buffer || !file.originalname) {
      throw new Error("Invalid or missing file");
    }

    const fileName = options.customFileName
      ? options.customFileName
      : options.generateUniqueName === false
      ? file.originalname
      : generateUniqueFileName(file.originalname);

    const result = await uploadToGCS(file, fileName, folder);

    return {
      success: true,
      message: "File uploaded successfully",
      data: {
        ...result,
        uploadedAt: new Date().toISOString(),
        folder: folder || null,
      },
    };
  } catch (error) {
    return {
      success: false,
      message: "Failed to upload file",
      error: error.message,
    };
  }
};

const deleteFile = async (fileName) => {
  try {
    if (!fileName) {
      return {
        success: false,
        error: "MISSING_FILENAME",
        message: "File name is required",
      };
    }

    await deleteFromGCS(fileName);

    return {
      success: true,
      message: "File deleted successfully",
      data: {
        fileName,
        deletedAt: new Date().toISOString(),
      },
    };
  } catch (error) {
    console.error("Error in deleteFile:", error);

    let errorCode = "DELETE_FAILED";
    let userMessage = "Failed to delete file";

    if (error.message.includes("not found") || error.message.includes("404")) {
      errorCode = "FILE_NOT_FOUND";
      userMessage = "File not found";
    }

    return {
      success: false,
      error: errorCode,
      message: userMessage,
      details: error.message,
      timestamp: new Date().toISOString(),
    };
  }
};

module.exports = {
  uploadFile,
  deleteFile,
  uploadToGCS,
  deleteFromGCS,
  generateUniqueFileName,
};
