const { Evm<PERSON>hain } = require("@moralisweb3/common-evm-utils");
const Moralis = require("moralis").default;

Moralis.start({
  apiKey:
    process.env.MORALIS_API_KEY ||
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJub25jZSI6IjU0Mzc0NTdmLThhZDAtNGViMC04OGM3LWY1ZjBkODNiNWQ4NCIsIm9yZ0lkIjoiNDA4ODM2IiwidXNlcklkIjoiNDIwMTA5IiwidHlwZUlkIjoiNjk3Zjg1NGEtZTYxZS00YWQ2LTliZDItNjMyMzA5OTQyMjM0IiwidHlwZSI6IlBST0pFQ1QiLCJpYXQiOjE3MjY3NDMxNTksImV4cCI6NDg4MjUwMzE1OX0.m2X7_RjAU7KXl6THPwjW6qGbZ9nQYCO-cmsTuE0pOsQ",
}).catch((error) => console.error("Failed to initialize Moralis:", error));

const getTokenPrice = async (tokenAddress) => {
  try {
    const response = await Moralis.EvmApi.token.getTokenPrice({
      address: tokenAddress,
      chain: EvmChain.ETHEREUM,
    });
    // console.log("Api response ", response).json();

    return response.toJSON();
  } catch (error) {
    console.error(`Error fetching price for ${tokenAddress}:`, error.message);
    return null;
  }
};

module.exports = { getTokenPrice };
