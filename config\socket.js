const socketIo = require('socket.io');
const sendPushNotification = require('../config/firebase');

const initializeSocket = (server) => {
  const io = socketIo(server, {
    cors: {
      origin: '*', 
      methods: ['GET', 'POST'],
    },
  });

  // Handle connection
  io.on('connection', (socket) => {
    console.log('A user connected:', socket.id);

    // User joins a room based on their user_id
    socket.on('join', (userId) => {
      socket.join(userId);
      console.log(`User ${userId} joined their notification room`);
    });

    // Send real-time notification and push notification
    socket.on('send-notification', async (notificationData) => {
      try {
        // Emit the notification to the specific user's room
        io.to(notificationData.user_id).emit('new-notification', {
          user_id: notificationData.user_id,
          image: notificationData.image || '',
          title: notificationData.title,
          content: notificationData.content,
          type: notificationData.type || '', 
          notiType: notificationData.notiType || 'info',
        });

        console.log(`Notification sent to user ${notificationData.user_id}`);

        // Send Firebase Push Notification
        if (notificationData.fcmToken) {
          await sendPushNotification(notificationData.fcmToken, notificationData.title, notificationData.content);
          console.log(`Push notification sent to user ${notificationData.user_id}`);
        }
        
      } catch (error) {
        console.error('Error sending notification:', error);
      }
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      console.log('User disconnected:', socket.id);
    });
  });

  return io;
};

module.exports = { initializeSocket };
