const User = require("../model/user");
const Points = require("../model/points");
const TokenSwap = require("../model/tokenSwaps");
const asyncHandler = require("express-async-handler");
const jwt = require("jsonwebtoken");
const { paginate } = require("../utils");
const RedemptionRequest = require("../model/redemptionRequest");

const generateReferralCode = () => {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let code = "";
  for (let i = 0; i < 4; i++) {
    code += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return code;
};

const registerUser = asyncHandler(async (req, res) => {
  const { ethWalletAddress, solanaWalletAddress, referralCode } = req.body;

  if (!ethWalletAddress && !solanaWalletAddress) {
    return res
      .status(400)
      .json({ message: "Please provide at least one wallet address" });
  }

  const existingUser = await User.findOne({
    $or: [
      { "walletAddress.ethWalletAddress": ethWalletAddress },
      { "walletAddress.solanaWalletAddress": solanaWalletAddress },
    ].filter((condition) => {
      return (
        Object.values(condition)[0] !== null &&
        Object.values(condition)[0] !== undefined
      );
    }),
  });

  if (existingUser) {
    return res
      .status(400)
      .json({ message: "User with provided wallet address already exists" });
  }

  let newReferralCode;
  while (true) {
    newReferralCode = generateReferralCode();
    const exists = await User.findOne({ referralCode: newReferralCode });
    if (!exists) break;
  }

  const walletObj = {
    ethWalletAddress: ethWalletAddress || null,
    solanaWalletAddress: solanaWalletAddress || null,
  };

  const user = await User.create({
    walletAddress: walletObj,
    referralCode: newReferralCode,
    referredBy: referralCode || null,
  });

  if (referralCode) {
    const referrer = await User.findOne({ referralCode });
    if (referrer) {
      referrer.totalPoints += 100;
      await referrer.save();

      const pointsRecord = new Points({
        walletAddress:
          referrer.walletAddress.ethWalletAddress ||
          referrer.walletAddress.solanaWalletAddress,
        pointsType: "refer",
        points: 100,
        description: `Referral bonus for inviting user`,
      });

      await pointsRecord.save();
    }
  }

  if (user) {
    res.status(201).json({
      _id: user.id,
      walletAddress: user.walletAddress,
      referralCode: user.referralCode,
      totalPoints: user.totalPoints,
    });
  } else {
    res.status(400);
    throw new Error("Invalid user data");
  }
});

const dailyCheckIn = asyncHandler(async (req, res) => {
  const { walletAddress } = req.body;

  if (!walletAddress) {
    return res.status(400).json({ message: "Wallet address is required" });
  }

  const user = await User.findOne({
    $or: [
      { "walletAddress.ethWalletAddress": walletAddress },
      { "walletAddress.solanaWalletAddress": walletAddress },
    ],
  });

  if (!user) {
    return res.status(400).json({ message: "User not found" });
  }

  const today = new Date();
  const lastLogin = user.lastLoginDate;

  const isNewLoginDay =
    !lastLogin || lastLogin.toDateString() !== today.toDateString();

  if (isNewLoginDay) {
    user.totalPoints += 10;
    user.lastLoginDate = today;
    await user.save();

    const pointsRecord = new Points({
      walletAddress:
        user.walletAddress.ethWalletAddress ||
        user.walletAddress.solanaWalletAddress,
      pointsType: "login",
      points: 10,
      description: "Daily login bonus",
    });
    await pointsRecord.save();
  }

  res.json({
    _id: user._id,
    walletAddress: user.walletAddress,
    totalPoints: user.totalPoints,
    message: isNewLoginDay
      ? "Daily checkIn successful. Daily checkIn points awarded."
      : "checkIn successful.",
  });
});

const getReferralStats = asyncHandler(async (req, res) => {
  const { walletAddress } = req.query;

  if (!walletAddress) {
    return res.status(400).json({ message: "walletAddress is required" });
  }

  const user = await User.findOne({
    $or: [
      { "walletAddress.ethWalletAddress": walletAddress },
      { "walletAddress.solanaWalletAddress": walletAddress },
    ],
  });

  if (!user) {
    return res.status(404).json({ message: "User not found" });
  }

  const referralCode = user.referralCode;

  const referredUsersCount = await User.countDocuments({
    referredBy: referralCode,
  });

  res.status(200).json({
    walletAddress: user.walletAddress,
    referralCode: referralCode,
    totalPoints: user.totalPoints,
    referredUsers: referredUsersCount,
  });
});

const getAllRegisteredUsers = asyncHandler(async (req, res) => {
  const {page} = req.query;
  const users = await User.find();
  const { paginatedItems, totalPages, currentPage, totalItems } = paginate(users, 15, page);
  res.status(200).json({
    data: paginatedItems,
    totalPages,
    currentPage,
    totalItems,
  });
});

const getUserStatus = asyncHandler(async (req, res) => {
  const { walletAddress } = req.query;
  if (!walletAddress) {
    return res.status(400).json({ message: "Wallet address required" });
  }
  const user = await User.findOne({
    $or: [
      { "walletAddress.ethWalletAddress": walletAddress },
      { "walletAddress.solanaWalletAddress": walletAddress },
    ],
  });
  if (!user) return res.status(404).json({ message: "User not found" });

  const swapResult = await TokenSwap.aggregate([
    { $match: { walletAddress: walletAddress } },
    {
      $group: {
        _id: "$walletAddress",
        totalSwapAmount: { $sum: "$inputAmount" },
      },
    },
  ]);
  const totalSwapAmount = swapResult.length > 0 ? swapResult[0].totalSwapAmount : 0;

  const thresholds = [
    { rank: "bronze", min: 0, max: 1000, swap: 100 },
    { rank: "silver", min: 1000, max: 10000, swap: 1000 },
    { rank: "gold", min: 10000, max: Infinity, swap: 10000 },
  ];
  
  const calculatedRank = thresholds.find(t => user.totalPoints >= t.min && user.totalPoints < t.max) || thresholds[2];
  
  if (user.rank !== calculatedRank.rank) {
    user.rank = calculatedRank.rank;
    await user.save();
  }
  
  const next = thresholds[thresholds.indexOf(calculatedRank) + 1] || null;

  // Calculate swap progress for each rank
  let bronzeSwapProgress = 0;
  let silverSwapProgress = 0;
  let goldSwapProgress = 0;

  if (totalSwapAmount >= 100) {
    bronzeSwapProgress = 100;
    const remainingForSilver = totalSwapAmount - 100;
    if (remainingForSilver > 0) {
      silverSwapProgress = Math.min(remainingForSilver, 1000);
      const remainingForGold = remainingForSilver - 1000;
      if (remainingForGold > 0) {
        goldSwapProgress = Math.min(remainingForGold, 10000);
      }
    }
  } else {
    bronzeSwapProgress = totalSwapAmount;
  }

  res.json({
    rank: calculatedRank.rank,
    totalPoints: user.totalPoints,
    bronzeSwapProgress,
    silverSwapProgress,
    goldSwapProgress,
    totalSwapAmount,
    nextRedemption: next ? { rank: next.rank, requiredSwap: next.swap } : null,
  });
});

const redeemPoints = asyncHandler(async (req, res) => {
  const { walletAddress } = req.body;
  if (!walletAddress) {
    return res.status(400).json({ message: "Wallet address required" });
  }
  const user = await User.findOne({
    $or: [
      { "walletAddress.ethWalletAddress": walletAddress },
      { "walletAddress.solanaWalletAddress": walletAddress },
    ],
  });

  // console.log("i am user",user);
  
  if (!user) return res.status(404).json({ message: "User not found" });

  if (user.totalPoints <= 0) {
    return res.status(400).json({ message: "No points available for redemption" });
  }

  const swapResult = await TokenSwap.aggregate([
    { $match: { walletAddress: walletAddress } },
    {
      $group: {
        _id: "$walletAddress",
        totalSwapAmount: { $sum: "$inputAmount" },
      },
    },
  ]);
  // console.log("i am swapResult",swapResult);

  
  const totalSwapAmount = swapResult.length > 0 ? swapResult[0].totalSwapAmount : 0;

  const thresholds = [
    { rank: "bronze", min: 0, max: 1000, swap: 100 },
    { rank: "silver", min: 1000, max: 10000, swap: 1000 },
    { rank: "gold", min: 10000, max: Infinity, swap: 10000 },
  ];
  
  const current = thresholds.find(t => user.totalPoints >= t.min && user.totalPoints < t.max) || thresholds[2];

  const hasRedeemedForRank = user.redeemedRanks && user.redeemedRanks.some(r => r.rank === current.rank);
  if (hasRedeemedForRank) {
    return res.status(400).json({ message: `You have already redeemed points for ${current.rank} rank.` });
  }

  if (current.rank === "bronze") {
    if (totalSwapAmount < 100) {
      return res.status(400).json({ message: `You need to complete $100 swap for bronze redemption. Current swap: $${totalSwapAmount}` });
    }
  } else if (current.rank === "silver") {
    if (totalSwapAmount < 1000) { // $1000 total swap for silver
      return res.status(400).json({ message: `You need to complete $1000 total swap for silver redemption. Current swap: $${totalSwapAmount}` });
    }
  } else if (current.rank === "gold") {
    if (totalSwapAmount < 10000) { // $10000 total swap for gold
      return res.status(400).json({ message: `You need to complete $10000 total swap for gold redemption. Current swap: $${totalSwapAmount}` });
    }
  }

  const pointsToRedeem = user.totalPoints;

  if (!user.redeemedRanks) {
    user.redeemedRanks = [];
  }
  user.redeemedRanks.push({
    rank: current.rank,
    redeemedAt: new Date(),
  });

  await user.save();
console.log("i am user",user.walletAddress.solanaWalletAddress);

  // Log for admin
  await RedemptionRequest.create({
    user: user._id,
    solanaWalletAddress: user.walletAddress.solanaWalletAddress || "kaduu",
    rank: current.rank,
    pointsRedeemed: pointsToRedeem,
    swapAmount: totalSwapAmount
  });

  res.json({
    message: `Redeemed ${pointsToRedeem} points for ${current.rank}. Total swap amount: $${totalSwapAmount}`,
    pointsRedeemed: pointsToRedeem,
    totalSwapAmount,
    redeemedRank: current.rank,
    user,
  });
});

// Get all redemptions (admin)
const getAllRedemptions = asyncHandler(async (req, res) => {
  const redemptions = await RedemptionRequest.find().sort({ createdAt: -1 });
  res.status(200).json(redemptions);
});

// Get redemptions by wallet address (admin)
const getRedemptionsByWalletAddress = asyncHandler(async (req, res) => {
  const { walletAddress } = req.query;
  if (!walletAddress) {
    return res.status(400).json({ message: "walletAddress is required" });
  }
  
  const redemptions = await RedemptionRequest.find({
    solanaWalletAddress: walletAddress,
  }).sort({ createdAt: -1 });
  res.status(200).json(redemptions);
});

module.exports = {
  registerUser,
  dailyCheckIn,
  getReferralStats,
  getAllRegisteredUsers,
  getUserStatus,
  redeemPoints,
  getAllRedemptions,
  getRedemptionsByWalletAddress,
};
