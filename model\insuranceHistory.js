const mongoose = require("mongoose");

const insuranceHistorySchema = new mongoose.Schema(
  {
    insuranceId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Insurance",
    },
    insuranceWalletAddress: {
      type: String,
    },
    insuranceCoverageAmount: {
      type: Number,
    },
    insuranceFee: {
      type: Number,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  },
  { timestamp: true }
);

const InsuranceHistory = mongoose.model(
  "InsuranceHistory",
  insuranceHistorySchema
);

module.exports = InsuranceHistory;
