const { Storage } = require("@google-cloud/storage");
const path = require("path");
require("dotenv").config();

const projectId = process.env.GOOGLE_CLOUD_PROJECT_ID;
const keyFilename = process.env.GOOGLE_CLOUD_KEY_FILE
  ? path.resolve(process.env.GOOGLE_CLOUD_KEY_FILE)
  : path.join(process.cwd(), "config/serviceAccountKey.json");
const BUCKET_NAME = process.env.GCS_BUCKET_NAME;

if (!projectId || !BUCKET_NAME) {
  return res.status(500).json({
    message: "Missing environment variables",
    error: "MISSING_ENV_VARS",
  });
}

const storage = new Storage({ projectId, keyFilename });
const bucket = storage.bucket(BUCKET_NAME);

const uploadToGCS = async (file, fileName, folder = "") => {
  try {
    if (!file?.buffer || (!file.originalname && !fileName)) {
      throw new Error("Invalid file");
    }

    const finalFileName = fileName || file.originalname;
    const fullFileName = folder ? `${folder}/${finalFileName}` : finalFileName;
    const fileRef = bucket.file(fullFileName);

    const contentType = file.mimetype || getContentType(finalFileName);

    await fileRef.save(file.buffer, {
      metadata: {
        contentType,
        cacheControl: "public, max-age=********",
      },
      resumable: false,
    });

    let isPublic = false;
    try {
      await fileRef.makePublic();
      isPublic = true;
    } catch (_) {}

    const publicUrl = `https://storage.googleapis.com/${BUCKET_NAME}/${fullFileName}`;
    const gsUrl = `gs://${BUCKET_NAME}/${fullFileName}`;

    let signedUrl = null;
    try {
      const [url] = await fileRef.getSignedUrl({
        action: "read",
        expires: Date.now() + 7 * 24 * 60 * 60 * 1000,
      });
      signedUrl = url;
    } catch (_) {}

    return {
      fileName: fullFileName,
      originalName: file.originalname,
      size: file.size,
      contentType,
      publicUrl: isPublic ? publicUrl : null,
      signedUrl,
      gsUrl,
      isPublic,
      success: true,
    };
  } catch (error) {
    throw new Error(`Failed to upload file: ${error.message}`);
  }
};

const deleteFromGCS = async (fileName) => {
  try {
    await bucket.file(fileName).delete();
    console.log(`File deleted successfully: ${fileName}`);
  } catch (error) {
    console.error("Error deleting from GCS:", error);
    throw new Error(`Failed to delete file: ${error.message}`);
  }
};

function getContentType(fileName) {
  const ext = path.extname(fileName).toLowerCase();

  const contentTypes = {
    ".jpg": "image/jpeg",
    ".jpeg": "image/jpeg",
    ".png": "image/png",
    ".gif": "image/gif",
    ".webp": "image/webp",
    ".avif": "image/avif",
    ".bmp": "image/bmp",
    ".svg": "image/svg+xml",
    ".tiff": "image/tiff",
    ".tif": "image/tiff",
    ".ico": "image/x-icon",
    ".heic": "image/heic",
    ".heif": "image/heif",
  };

  return contentTypes[ext] || "application/octet-stream";
}

const generateUniqueFileName = (originalName) => {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 8);
  const ext = path.extname(originalName);
  const nameWithoutExt = path.basename(originalName, ext);
  return `${nameWithoutExt}_${timestamp}_${randomString}${ext}`;
};

module.exports = {
  uploadToGCS,
  deleteFromGCS,
  generateUniqueFileName,
};
