const dbConnection = require("./config/server.js");
const express = require("express");
const cors = require("cors");
const DCACronJob = require("./services/cron-job.js");

const Transcation = require("./model/transcation.js");
const {
  Connection,
  PublicKey,
  TransactionInstruction,
  VersionedTransaction,
  TransactionMessage,
  Keypair,
  LAMPORTS_PER_SOL,
  SystemProgram,
} = require("@solana/web3.js"); // Import Solana libraries

const {
  getAssociatedTokenAddress,
  createAssociatedTokenAccountInstruction,
  createTransferInstruction,
} = require("@solana/spl-token");
const {
  instructionDataToTransactionInstruction,
  getAdressLookupTableAccounts,
} = require("./utils"); // Import your utility function
require("dotenv").config();
const swaggerUi = require("swagger-ui-express");
const swaggerDocument = require("./config/swagger-output.json");
const http = require("http");
const { initializeSocket } = require("./config/socket.js");

const { Wallet } = require("@project-serum/anchor");
const { bs58 } = require("@project-serum/anchor/dist/cjs/utils/bytes");
const transakRoutes = require("./routes/transakRoutes");
const insuranceRoutes = require("./routes/insurance.js");
const WalletRoutes = require("./routes/wallet.js");
const confirmPaymentWalletRoutes = require("./routes/confirmPaymentAddress.js");
const paymentTrackingRoutes = require("./routes/paymentTrackingRoutes");
const adminRoutes = require("./routes/admin.js");
const ethSwap = require("./routes/ethSwap.js");
const DCA = require("./routes/dca.js");
const TokenSwap = require("./routes/tokenSwapsRoute.js");
const changellyRoutes = require("./routes/changellyRoutes.js");
const TokenSwaps = require("./model/tokenSwaps.js");
const contractAddress = require("./routes/contractAddress.js");
const userRoutes = require("./routes/userRoutes");
const taskRoutes = require("./routes/taskRoutes");
const pointsRoutes = require("./routes/pointsRoutes");
const socialRoutes = require("./routes/verifySocials.js");
require("dotenv").config();

const app = express();
app.use(express.json());
app.use(
  cors({
    origin: "*",
    methods: ["GET", "POST", "PUT", "DELETE"],
    credentials: true, // Allow cross-origin requests with credentials (cookies, etc.)
  })
);

//socket setup
const server = http.createServer(app);
initializeSocket(server);

// Initialize Solana Connection
const SOLANA_RPC_URL =
  process.env.SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com"; // Default to mainnet
const SOLANA_WS_URL =
  process.env.SOLANA_WS_URL || "wss://api.mainnet-beta.solana.com"; // Default to mainnet

const connection = new Connection(SOLANA_RPC_URL, {
  commitment: "confirmed", // Use 'confirmed' commitment level
  wsEndpoint: SOLANA_WS_URL, // WebSocket endpoint
});

const MAX_RETRIES = 20; // Maximum number of retries
const RETRY_DELAY_MS = 2000; // Delay between retries in milliseconds

app.post("/submit-quote", async (req, res) => {
  const { inputToken, outputToken, amount, slipage, walletAddress, secretKey } =
    req.body;

  // Your fee wallet address and token account
  const FEE_WALLET = new PublicKey(
    "********************************************"
  );
  const FEE_PERCENTAGE = 0.0085; // 0.85%

  try {
    // Step 1: Fetch a quote from Jupiter API
    const quoteResponse = await (
      await fetch(
        `https://quote-api.jup.ag/v6/quote?inputMint=${inputToken}&outputMint=${outputToken}&amount=${amount}&slippageBps=${slipage}`
      )
    ).json();

    const secretKeyUint8Array = bs58.decode(secretKey);
    const wallet = new Wallet(Keypair.fromSecretKey(secretKeyUint8Array));

    // Step 2: Validate the quoteResponse object
    if (!quoteResponse || !quoteResponse.inAmount || !quoteResponse.outAmount) {
      throw new Error("Invalid quote response from Jupiter API");
    }

    // Calculate fee amount (0.85% of the input amount)
    const feeAmount = Math.floor(
      Number(quoteResponse.inAmount) * FEE_PERCENTAGE
    );

    console.log("this is fee amount", feeAmount);

    // Get the input token mint
    const inputMint = new PublicKey(inputToken);

    // Create fee instruction based on token type
    let feeInstruction;

    if (inputToken === "So11111111111111111111111111111111111111112") {
      // SOL transfer
      feeInstruction = SystemProgram.transfer({
        fromPubkey: wallet.publicKey,
        toPubkey: FEE_WALLET,
        lamports: feeAmount,
      });
    } else {
      feeInstruction = SystemProgram.transfer({
        fromPubkey: wallet.publicKey,
        toPubkey: FEE_WALLET,
        lamports: Math.floor(Number(quoteResponse.outAmount) * FEE_PERCENTAGE),
      });
    }

    const STATIC_PRIORITY_FEE_SOL = 0.00009;

    // Step 3: Send the swap request to Jupiter API
    const swapResponse = await (
      await fetch("https://quote-api.jup.ag/v6/swap-instructions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          quoteResponse,
          userPublicKey: walletAddress,
          prioritizationFeeLamports: Math.floor(
            STATIC_PRIORITY_FEE_SOL * LAMPORTS_PER_SOL
          ),
        }),
      })
    ).json();

    const memo = `Gaya Wallet Swap-${Date.now()}`;
    const memoProgramId = new PublicKey(
      "MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"
    );
    const memoInstruction = {
      keys: [],
      programId: memoProgramId,
      data: Buffer.from(memo),
    };

    const {
      computeBudgetInstructions,
      setupInstructions,
      swapInstruction,
      cleanupInstruction,
      addressLookupTableAddresses,
    } = swapResponse;

    // Include fee transfer instruction(s) in the transaction
    const instructions = [
      ...computeBudgetInstructions.map(instructionDataToTransactionInstruction),
      ...setupInstructions.map(instructionDataToTransactionInstruction),
      ...(Array.isArray(feeInstruction) ? feeInstruction : [feeInstruction]),
      instructionDataToTransactionInstruction(swapInstruction),
      instructionDataToTransactionInstruction(cleanupInstruction),
      memoInstruction,
    ].filter(Boolean);

    // Rest of your transaction construction and sending logic...
    const { blockhash } = await connection.getLatestBlockhash();

    const addressLookupTableAccounts = await getAdressLookupTableAccounts(
      addressLookupTableAddresses,
      connection
    );

    const versionedTransaction = new VersionedTransaction(
      new TransactionMessage({
        payerKey: wallet.publicKey,
        recentBlockhash: blockhash,
        instructions,
      }).compileToV0Message(addressLookupTableAccounts)
    );

    versionedTransaction.sign([wallet.payer]);
    const signature = await connection.sendTransaction(versionedTransaction, {
      skipPreflight: true,
      maxRetries: 20,
    });
    // const tshashs = new Transcation({ txHash: signature });
    // await tshashs.save();
    // Step 4: Wait for transaction confirmation using `getSignatureStatuses` with retries

    const tokenSwapData = await TokenSwaps.create({
      walletAddress,
      inputToken,
      outputToken,
      inputAmount: amount,
      transactionHash: signature,
    });
    await tokenSwapData.save();

    // Step 5: Send the swap response back to the client
    res.status(200).json({
      signature,
      status: "Sending",
      message: "Sending Transcation..",
    });
  } catch (error) {
    console.error("Error forwarding quote to Jupiter API:", error);
    res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
});

app.post("/confirmTranscation", async (req, res) => {
  const { signature } = req.body;

  try {
    let retries = 0;
    let confirmationStatus;

    while (retries < MAX_RETRIES) {
      try {
        // Use `getSignatureStatuses` to check the transaction status
        const statusResponse = await connection.getSignatureStatuses(
          [signature],
          {
            searchTransactionHistory: true, // Ensure the transaction is searched in history
          }
        );

        confirmationStatus = statusResponse.value[0];

        if (!confirmationStatus) {
          throw new Error("Transaction status not found.");
        }

        if (confirmationStatus.err) {
          // throw new Error(
          //   "Transaction failed: " + JSON.stringify(confirmationStatus.err)
          // );
          return res.status(400).json({
            signature,
            status: confirmationStatus.confirmationStatus,
            message:
              "Transaction failed: " + JSON.stringify(confirmationStatus.err),
          });
        }

        // Check if the transaction is confirmed
        if (
          confirmationStatus.confirmationStatus === "confirmed" ||
          confirmationStatus.confirmationStatus === "finalized"
        ) {
          console.log(
            "Transaction confirmed with status:",
            confirmationStatus.confirmationStatus
          );
          break;
        }

        // If not confirmed, wait and retry
        retries++;
        console.warn(
          `Retry ${retries}/${MAX_RETRIES}: Transaction not yet confirmed.`
        );

        if (retries >= MAX_RETRIES) {
          throw new Error(
            "Max retries reached. Transaction confirmation failed."
          );
        }

        // Wait before retrying
        await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY_MS));
      } catch (error) {
        retries++;
        console.warn(`Retry ${retries}/${MAX_RETRIES}:`, error.message);

        if (retries >= MAX_RETRIES) {
          throw new Error(
            "Max retries reached. Transaction confirmation failed."
          );
        }

        // Wait before retrying
        await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY_MS));
      }
    }

    res.status(200).json({
      signature,
      status: confirmationStatus.confirmationStatus,
      message: "Transaction successfully confirmed on the blockchain.",
    });
  } catch (error) {
    console.error("Error forwarding quote to Jupiter API:", error);
    res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
});

app.get("/urls", (req, res) => {
  try {
    if (!SOLANA_RPC_URL || !SOLANA_WS_URL) {
      throw new Error("Missing required environment variables");
    }

    return res.status(200).json({
      rpcUrl: SOLANA_RPC_URL,
      wsUrl: SOLANA_WS_URL,
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: error.message,
    });
  }
});

app.get("/", (req, res) => {
  res.send("API is running...");
});

app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerDocument));
app.use("/insurance", insuranceRoutes);
app.use("/wallet", WalletRoutes);
app.use("/transak", transakRoutes);
app.use("/payment-tracking", paymentTrackingRoutes);
app.use("/admin", adminRoutes);
app.use("/walletAddress", confirmPaymentWalletRoutes);
app.use("/evm", ethSwap);
app.use("/dca", DCA);
app.use("/token", TokenSwap);
app.use("/user", userRoutes);
app.use("/task", taskRoutes);
app.use("/points", pointsRoutes);
app.use("/", contractAddress);
app.use("/socials", socialRoutes);
changellyRoutes(app);

const PORT = 7000;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log("Initializing DCA cron jobs...");
  // DCACronJob();
});

dbConnection();
