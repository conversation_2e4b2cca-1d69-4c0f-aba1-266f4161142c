{"swagger": "2.0", "info": {"title": "Your API Title", "description": "API documentation description", "version": "1.0.0"}, "host": "localhost:7000", "basePath": "/", "schemes": ["http"], "paths": {"/admin/login": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"email": {"example": "any"}, "password": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "500": {"description": "Internal Server Error"}}}}, "/insurance/create-Insurance": {"post": {"description": "", "parameters": [{"name": "api-key", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"walletAddress": {"example": "any"}, "name": {"example": "any"}, "email": {"example": "any"}, "insuranceCoverageAmount": {"example": "any"}, "paymentFrequency": {"example": "any"}, "cardType": {"example": "any"}, "cvv": {"example": "any"}, "preferredType": {"example": "any"}}}}], "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "500": {"description": "Internal Server Error"}}}}, "/insurance/get-Insurance": {"get": {"description": "", "parameters": [{"name": "api-key", "in": "header", "type": "string"}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "500": {"description": "Internal Server Error"}}}}, "/insurance/update-payment-status": {"post": {"description": "", "parameters": [{"name": "api-key", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"insuranceId": {"example": "any"}, "txHash": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/insurance/get-insurance/{insuranceId}": {"get": {"description": "", "parameters": [{"name": "insuranceId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/wallet/{wallet}": {"get": {"description": "", "parameters": [{"name": "wallet", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}}}}, "/transak/create-transaction": {"post": {"description": "", "parameters": [{"name": "network", "in": "query", "type": "string"}, {"name": "wallet<PERSON>ddress", "in": "query", "type": "string"}, {"name": "fiatAmount", "in": "query", "type": "string"}, {"name": "defaultFiatCurrency", "in": "query", "type": "string"}, {"name": "cryptoCurrencyCode", "in": "query", "type": "string"}], "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/transak/get-currencies": {"get": {"description": "", "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/transak/get-crypto-currencies": {"get": {"description": "", "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/wallet/postWalletAdress": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"walletAddress": {"example": "any"}}}}], "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request"}}}}, "/wallet/getWalletAdress": {"get": {"description": "", "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/walletAddress/confirm-PaymentWallet": {"post": {"description": "", "responses": {"default": {"description": ""}}}}, "/evm/swap": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"transactionType": {"example": "any"}, "inputTokenAddress": {"example": "any"}, "outputTokenAddress": {"example": "any"}, "inputAmount": {"example": "any"}, "inputDecimals": {"example": "any"}, "outPutDecimals": {"example": "any"}, "InputtokenName": {"example": "any"}, "outPuttokenName": {"example": "any"}, "Inputsymbol": {"example": "any"}, "outputsymbol": {"example": "any"}, "address": {"example": "any"}, "chainId": {"example": "any"}, "slipage": {"example": "any"}, "isNative": {"example": "any"}, "isNativeOut": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "500": {"description": "Internal Server Error"}}}}, "/evmswap-execute": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"tokenInAddress": {"example": "any"}, "tokenInDecimal": {"example": "any"}, "tokenInSymbol": {"example": "any"}, "tokenInName": {"example": "any"}, "tokenOutAddress": {"example": "any"}, "tokenOutDecimal": {"example": "any"}, "tokenOutSymbol": {"example": "any"}, "tokenOutName": {"example": "any"}, "amountIn": {"example": "any"}, "slippageTolerance": {"example": "any"}, "recipientAddress": {"example": "any"}, "isNative": {"example": "any"}, "signature": {"example": "any"}, "permit": {"example": "any"}, "chainID": {"example": "any"}, "isNativeOut": {"example": "any"}, "privateKey": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "500": {"description": "Internal Server Error"}}}}, "/evm/getSignedData": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"tokenInAddress": {"example": "any"}, "tokenInDecimal": {"example": "any"}, "amountIn": {"example": "any"}, "recipientAddress": {"example": "any"}, "chainID": {"example": "any"}, "privateKey": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}}}}, "/evm/token-list": {"get": {"description": "", "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/dca/create-dca-order": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"user": {"example": "any"}, "inputMint": {"example": "any"}, "outputMint": {"example": "any"}, "inAmount": {"example": "any"}, "numberOfOrders": {"example": "any"}, "interval": {"example": "any"}, "maxPrice": {"example": "any"}, "minPrice": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "500": {"description": "Internal Server Error"}, "503": {"description": "Service Unavailable"}}}}, "/dca/get-dca-order": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"user": {"example": "any"}, "recurringType": {"example": "any"}, "orderStatus": {"example": "any"}, "page": {"example": "any"}, "limit": {"example": "any"}, "includeFailedTX": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "500": {"description": "Internal Server Error"}}}}, "/dca/get-dca-active-order": {"post": {"description": "", "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/dca/startDCA": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"walletAddress": {"example": "any"}, "inputTokenAddress": {"example": "any"}, "tokenInName": {"example": "any"}, "tokenOutName": {"example": "any"}, "outPutDecimals": {"example": "any"}, "inputDecimals": {"example": "any"}, "outPutSymbol": {"example": "any"}, "inputSymbol": {"example": "any"}, "ChainId": {"example": "any"}, "outputTokenAddress": {"example": "any"}, "category": {"example": "any"}, "amount": {"example": "any"}, "allocationCurrency": {"example": "any"}, "currency": {"example": "any"}, "parts": {"example": "any"}, "action": {"example": "any"}, "minPrice": {"example": "any"}, "maxPrice": {"example": "any"}, "totalDuration": {"example": "any"}, "durationUnit": {"example": "any"}, "portfolioIncreaseAmount": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "500": {"description": "Internal Server Error"}}}}, "/dca/stopDCA/{dcaId}": {"post": {"description": "", "parameters": [{"name": "dcaId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/dca/history/{walletAddress}": {"get": {"description": "", "parameters": [{"name": "wallet<PERSON>ddress", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/dca/dcas/{walletAddress}": {"get": {"description": "", "parameters": [{"name": "wallet<PERSON>ddress", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/dca/dca-history/{id}": {"get": {"description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/token/tokenSwap": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"walletAddress": {"example": "any"}, "inputToken": {"example": "any"}, "outputToken": {"example": "any"}, "inputAmount": {"example": "any"}, "transactionHash": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}}}}, "/token/walletAmount/{walletAddress}": {"get": {"description": "", "parameters": [{"name": "wallet<PERSON>ddress", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}}}}, "/token/buyTranscation": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"walletAddress": {"example": "any"}, "amount": {"example": "any"}, "name": {"example": "any"}, "currencyBought": {"example": "any"}, "fiatCurrency": {"example": "any"}, "amountReceived": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}}}}, "/token/sellTranscation": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"walletAddress": {"example": "any"}, "sellAmount": {"example": "any"}, "name": {"example": "any"}, "amount": {"example": "any"}, "currencySold": {"example": "any"}, "fiatCurrency": {"example": "any"}, "amountSold": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}}}}, "/token/getAllTokenAirDrop": {"get": {"description": "", "parameters": [{"name": "tier", "in": "query", "type": "string"}, {"name": "minDca", "in": "query", "type": "string"}, {"name": "maxDca", "in": "query", "type": "string"}, {"name": "minSwap", "in": "query", "type": "string"}, {"name": "maxSwap", "in": "query", "type": "string"}, {"name": "fromDate", "in": "query", "type": "string"}, {"name": "toDate", "in": "query", "type": "string"}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}}}}, "/changelly-fiat/providers-list": {"get": {"description": "", "responses": {"200": {"description": "OK"}}}}, "/changelly-fiat/currency-list": {"get": {"description": "", "parameters": [{"name": "type", "in": "query", "type": "string"}, {"name": "providerCode", "in": "query", "type": "string"}, {"name": "currency", "in": "query", "type": "string"}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/changelly-fiat/country-availability-list": {"get": {"description": "", "parameters": [{"name": "providerCode", "in": "query", "type": "string"}], "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/changelly-fiat/get-offers": {"post": {"description": "", "parameters": [{"name": "type", "in": "query", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"externalUserId": {"example": "any"}, "currencyFrom": {"example": "any"}, "currencyTo": {"example": "any"}, "amountFrom": {"example": "any"}, "country": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "500": {"description": "Internal Server Error"}}}}, "/changelly-fiat/validate-walllet-address": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"currency": {"example": "any"}, "walletExtraId": {"example": "any"}, "walletAddress": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}}}}, "/changelly-fiat/create-order": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"currencyFrom": {"example": "any"}, "currencyTo": {"example": "any"}, "amountFrom": {"example": "any"}, "country": {"example": "any"}, "state": {"example": "any"}, "walletExtraId": {"example": "any"}, "paymentMethod": {"example": "any"}, "userAgent": {"example": "any"}, "metadata": {"example": "any"}, "walletAddress": {"example": "any"}, "ip": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}}}}, "/changelly-fiat/create-order-v2": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"currencyFrom": {"example": "any"}, "currencyTo": {"example": "any"}, "amountFrom": {"example": "any"}, "country": {"example": "any"}, "state": {"example": "any"}, "walletExtraId": {"example": "any"}, "paymentMethod": {"example": "any"}, "userAgent": {"example": "any"}, "metadata": {"example": "any"}, "walletAddress": {"example": "any"}, "ip": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/changelly-fiat/create-order-v3": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"providerCode": {"example": "any"}, "currencyFrom": {"example": "any"}, "currencyTo": {"example": "any"}, "amountFrom": {"example": "any"}, "country": {"example": "any"}, "state": {"example": "any"}, "walletExtraId": {"example": "any"}, "paymentMethod": {"example": "any"}, "userAgent": {"example": "any"}, "metadata": {"example": "any"}, "walletAddress": {"example": "any"}, "ip": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}}}}, "/changelly-fiat/getOffers": {"post": {"description": "", "parameters": [{"name": "type", "in": "query", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"externalUserId": {"example": "any"}, "currencyFrom": {"example": "any"}, "currencyTo": {"example": "any"}, "amountFrom": {"example": "any"}, "country": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "500": {"description": "Internal Server Error"}}}}, "/changelly-fiat/sell-order": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"currencyFrom": {"example": "any"}, "currencyTo": {"example": "any"}, "amountFrom": {"example": "any"}, "country": {"example": "any"}, "walletAddress": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/changelly-fiat/sell-order-v2": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"currencyFrom": {"example": "any"}, "currencyTo": {"example": "any"}, "amountFrom": {"example": "any"}, "country": {"example": "any"}, "walletAddress": {"example": "any"}, "state": {"example": "any"}, "ip": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/changelly-fiat/sell-order-v3": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"currencyFrom": {"example": "any"}, "currencyTo": {"example": "any"}, "amountFrom": {"example": "any"}, "country": {"example": "any"}, "walletAddress": {"example": "any"}, "providerCode": {"example": "any"}, "paymentMethod": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/changelly-fiat/currency-list-network": {"get": {"description": "", "parameters": [{"name": "type", "in": "query", "type": "string"}, {"name": "network", "in": "query", "type": "string"}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/changelly-fiat/register-webhook": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"callbackUrl": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "500": {"description": "Internal Server Error"}}}}, "/changelly-fiat/webhook": {"post": {"description": "", "parameters": [{"name": "x-api-signature", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"event": {"example": "any"}, "data": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized"}}}}, "/changelly-fiat/get-order-status/{externalOrderId}": {"get": {"description": "", "parameters": [{"name": "externalOrderId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/user/register": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"walletAddress": {"example": "any"}, "referralCode": {"example": "any"}}}}], "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request"}}}}, "/user/login": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"walletAddress": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}}}}, "/task/createTask": {"post": {"description": "", "parameters": [{"name": "authorization", "in": "header", "type": "string"}, {"name": "Authorization", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"title": {"example": "any"}, "description": {"example": "any"}, "url": {"example": "any"}, "category": {"example": "any"}, "requirements": {"example": "any"}, "points": {"example": "any"}}}}], "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/task/getAllTasks": {"get": {"description": "", "parameters": [{"name": "category", "in": "query", "type": "string"}], "responses": {"200": {"description": "OK"}}}}, "/task/getTask/{taskId}": {"get": {"description": "", "parameters": [{"name": "taskId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found"}}}}, "/task/performTask/{walletAddress}/{taskId}": {"post": {"description": "", "parameters": [{"name": "wallet<PERSON>ddress", "in": "path", "required": true, "type": "string"}, {"name": "taskId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found"}}}}, "/task/deleteTask/{taskId}": {"delete": {"description": "", "parameters": [{"name": "taskId", "in": "path", "required": true, "type": "string"}, {"name": "authorization", "in": "header", "type": "string"}, {"name": "Authorization", "in": "header", "type": "string"}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Not Found"}}}}, "/task/updateTask/{taskId}": {"put": {"description": "", "parameters": [{"name": "taskId", "in": "path", "required": true, "type": "string"}, {"name": "authorization", "in": "header", "type": "string"}, {"name": "Authorization", "in": "header", "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"title": {"example": "any"}, "description": {"example": "any"}, "url": {"example": "any"}, "category": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Not Found"}}}}, "/points/walletPoints/{walletAddress}": {"get": {"description": "", "parameters": [{"name": "wallet<PERSON>ddress", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found"}}}}, "/contractAddress": {"post": {"description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"contractAddress": {"example": "any"}}}}], "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request"}, "500": {"description": "Internal Server Error"}}}}, "/getContractAddress": {"get": {"description": "", "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}}}