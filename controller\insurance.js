const Insurance = require("../model/insurance");
const asyncHandler = require("express-async-handler");
const InsuranceHistory = require("../model/insuranceHistory");
const FeePercentage = require("../model/feePercentage");

const createInsurance = asyncHandler(async (req, res) => {
  try {
    const API_KEY = req.headers["api-key"];
    if (!API_KEY) {
      return res.status(401).json({ message: "API key is required" });
    }

    const API_KEY_ENV = process.env.API_KEY_SERVER;
    if (API_KEY !== API_KEY_ENV) {
      return res.status(403).json({ message: "API key is incorrect" });
    }

    const {
      walletAddress,
      name,
      email,
      insuranceCoverageAmount,
      nationality,
      nationalId,
      paymentFrequency,
      cardType,
      cardExpiryDate,
      cvv,
      preferredType,
    } = req.body;

    if (
      !walletAddress ||
      !name ||
      !email ||
      !insuranceCoverageAmount ||
      !nationality ||
      !nationalId ||
      !paymentFrequency
    ) {
      return res
        .status(400)
        .json({ message: "All required fields must be provided" });
    }

    const now = new Date();

    // Check if insurance already exists for this user
    const existingInsurance = await Insurance.findOne({ walletAddress });

    // If user already has insurance
    if (existingInsurance) {
      if (
        existingInsurance.paid &&
        existingInsurance.insuranceExpiryDate > now
      ) {
        // If insurance is paid and still active → block new creation
        return res.status(409).json({
          message: `You already have an active insurance that expires on ${existingInsurance.insuranceExpiryDate.toLocaleString()}`,
        });
      }

      // Insurance is expired or unpaid → update the existing one
      existingInsurance.name = name;
      existingInsurance.email = email;
      existingInsurance.insuranceCoverageAmount = insuranceCoverageAmount;
      existingInsurance.nationality = nationality;
      existingInsurance.nationalId = nationalId;
      existingInsurance.paymentFrequency = paymentFrequency;
      existingInsurance.cardType = cardType;
      existingInsurance.cardExpiryDate = cardExpiryDate;
      existingInsurance.cvv = cvv;
      existingInsurance.preferredType = preferredType;
      existingInsurance.paid = false;
      existingInsurance.txHash = null;
      existingInsurance.insuranceFee = null;
      existingInsurance.newlyCreated = true;

      await existingInsurance.save();

      return res.status(200).json({
        message:
          "unpaid & expired insurance updated. Please proceed to payment.",
        insurance: existingInsurance,
      });
    }

    // If no insurance exists → create new one
    const newInsurance = new Insurance({
      walletAddress,
      name,
      email,
      insuranceCoverageAmount,
      nationality,
      nationalId,
      paymentFrequency,
      cardType,
      cardExpiryDate,
      cvv,
      preferredType,
      newlyCreated: true,
    });

    await newInsurance.save();

    res.status(201).json({
      message: "New insurance created successfully",
      insurance: newInsurance,
    });
  } catch (error) {
    console.error("Error creating insurance:", error);
    res.status(500).json({ error: "Server Error" });
  }
});

const getInsurance = asyncHandler(async (req, res) => {
  try {
    const API_KEY = req.headers["api-key"];
    if (!API_KEY) {
      return res.status(401).json({ message: "API key is required" });
    }

    const API_KEY_ENV = process.env.API_KEY_SERVER;
    if (API_KEY !== API_KEY_ENV) {
      return res.status(403).json({ message: "Invalid API key" });
    }

    const insurance = await Insurance.find();

    if (!insurance || insurance.length === 0) {
      return res.status(200).json({ message: "No insurance records found" });
    }

    res.status(200).json({ insurance });
  } catch (error) {
    console.error("Error fetching insurance:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

const updatePaymentStatus = asyncHandler(async (req, res) => {
  try {
    const API_KEY = req.headers["api-key"];

    if (!API_KEY) {
      return res.status(401).json({ message: "API key is required" });
    }

    const API_KEY_ENV = process.env.API_KEY_SERVER;
    if (API_KEY !== API_KEY_ENV) {
      return res.status(403).json({ message: "Invalid API key" });
    }

    const { insuranceId, txHash, insuranceFee } = req.body;

    if (!insuranceId || !txHash || !insuranceFee) {
      return res
        .status(400)
        .json({ message: "Insurance ID and txHash are required" });
    }

    const insurance = await Insurance.findById(insuranceId);

    if (!insurance) {
      return res.status(404).json({ message: "Insurance record not found" });
    }

    if (insurance) {
      let nowDate = new Date();

      if (insurance.insuranceExpiryDate > nowDate) {
        return res
          .status(400)
          .json({ message: "Insurance plan isn't expired yet." });
      }
    }

    const previosInsurance = await Insurance.findOne({
      walletAddress: insurance.walletAddress,
      newlyCreated: false,
    }).sort({ createdAt: -1 });

    // console.log("i am previos insurance", previosInsurance);

    let expiryDate;
    let now = new Date(); // Use Date object instead of timestamp
    let frequency;

    if (insurance.paymentFrequency === "monthly") {
      frequency = 30 * 24 * 60 * 60 * 1000; // Convert days to milliseconds
    } else if (insurance.paymentFrequency === "yearly") {
      frequency = 365 * 24 * 60 * 60 * 1000;
    }

    if (previosInsurance && previosInsurance.insuranceExpiryDate) {
      let diff = previosInsurance.insuranceExpiryDate - now;

      if (diff <= 0) {
        expiryDate = new Date(now.getTime() + frequency);
      } else {
        expiryDate = new Date(
          previosInsurance.insuranceExpiryDate.getTime() + frequency
        );
      }
    } else {
      expiryDate = new Date(now.getTime() + frequency);
    }

    // console.log("i am expiry date", expiryDate);

    insurance.paid = true;
    insurance.txHash = txHash;
    insurance.insuranceExpiryDate = expiryDate;
    insurance.newlyCreated = false;
    insurance.insuranceFee = insuranceFee;
    await insurance.save();

    const insuranceHistory = new InsuranceHistory({
      insuranceId: insurance.id,
      insuranceCoverageAmount: insurance.insuranceCoverageAmount,
      insuranceWalletAddress: insurance.walletAddress,
      insuranceFee: insurance.insuranceFee,
    });
    await insuranceHistory.save();
    console.log("i am insurance history", insuranceHistory);

    res.status(200).json({
      message: "Payment status updated successfully",
      insurance,
    });
  } catch (error) {
    console.error("Error updating payment status:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

const getInsuranceById = asyncHandler(async (req, res) => {
  try {
    const { insuranceId } = req.params;
    const insurance = await Insurance.findById(insuranceId);
    if (!insurance) {
      return res.status(404).json({ message: "Insurance record not found" });
    }
    const now = new Date();
    const expiryDate = new Date(insurance.insuranceExpiryDate);
    if (expiryDate < now) {
      return res.status(200).json({ message: "Insurance plan expired" });
    }
    res.status(200).json({ message: "Insurance plan is active", insurance });
  } catch (error) {
    console.error("Error fetching insurance:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

const getDashboardInsuredUsers = asyncHandler(async (req, res) => {
  try {
    const {
      search,
      page = 1,
      limit = 10,
      subscriptionType,
      minInsuredAmount,
      maxInsuredAmount,
      minInsuranceFee,
      maxInsuranceFee,
    } = req.query;

    const query = { paid: true };

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: "i" } },
        { email: { $regex: search, $options: "i" } },
        { walletAddress: { $regex: search, $options: "i" } },
      ];
    }

    if (subscriptionType) {
      query.paymentFrequency = subscriptionType;
    }
    if (minInsuredAmount) {
      query.insuranceCoverageAmount = {
        ...query.insuranceCoverageAmount,
        $gte: Number(minInsuredAmount),
      };
    }
    if (maxInsuredAmount) {
      query.insuranceCoverageAmount = {
        ...query.insuranceCoverageAmount,
        $lte: Number(maxInsuredAmount),
      };
    }
    if (minInsuranceFee) {
      query.insuranceFee = {
        ...query.insuranceFee,
        $gte: Number(minInsuranceFee),
      };
    }
    if (maxInsuranceFee) {
      query.insuranceFee = {
        ...query.insuranceFee,
        $lte: Number(maxInsuranceFee),
      };
    }

    const filteredCount = await Insurance.countDocuments(query);
    const totalPages = Math.ceil(filteredCount / limit) || 1;
    const currentPage = Math.max(1, Math.min(parseInt(page), totalPages));

    const insurance = await Insurance.find(query)
      .sort({ createdAt: -1 })
      .skip((currentPage - 1) * parseInt(limit))
      .limit(parseInt(limit));

    // Aggregate total stats for current period (last 7 days)
    const totalStats = await Insurance.aggregate([
      { $match: { paid: true } },
      {
        $group: {
          _id: null,
          totalCoverageAmount: { $sum: "$insuranceCoverageAmount" },
          totalRevenue: { $sum: "$insuranceFee" },
          totalInsuredWallets: { $sum: 1 },
        },
      },
    ]);

    const totalCoverageAmount = totalStats[0]?.totalCoverageAmount || 0;
    const totalRevenue = totalStats[0]?.totalRevenue || 0;
    const totalInsuredWallets = totalStats[0]?.totalInsuredWallets || 0;

    // Fetch last 7 days data
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 6);
    sevenDaysAgo.setHours(0, 0, 0, 0);

    const last7DaysData = await Insurance.aggregate([
      {
        $match: {
          paid: true,
          createdAt: { $gte: sevenDaysAgo },
        },
      },
      {
        $group: {
          _id: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
          insuredWallets: { $sum: 1 },
          totalCoverageAmount: { $sum: "$insuranceCoverageAmount" },
          totalRevenue: { $sum: "$insuranceFee" },
        },
      },
      { $sort: { _id: 1 } },
    ]);

    // Prepare last 7 days data
    const last7DaysMap = new Map();
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateString = date.toISOString().split("T")[0];
      last7DaysMap.set(dateString, {
        insuredWallets: 0,
        totalCoverageAmount: 0,
        totalRevenue: 0,
      });
    }

    last7DaysData.forEach((day) => {
      last7DaysMap.set(day._id, day);
    });

    const last7DaysArray = Array.from(last7DaysMap.values());

    // Fetch previous 7 days data (from 14 days ago to 7 days ago)
    const fourteenDaysAgo = new Date();
    fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 13);
    fourteenDaysAgo.setHours(0, 0, 0, 0);

    const sevenDaysAgoEnd = new Date();
    sevenDaysAgoEnd.setDate(sevenDaysAgoEnd.getDate() - 7);
    sevenDaysAgoEnd.setHours(23, 59, 59, 999);

    const previous7DaysData = await Insurance.aggregate([
      {
        $match: {
          paid: true,
          createdAt: { $gte: fourteenDaysAgo, $lte: sevenDaysAgoEnd },
        },
      },
      {
        $group: {
          _id: null,
          insuredWallets: { $sum: 1 },
          totalCoverageAmount: { $sum: "$insuranceCoverageAmount" },
          totalRevenue: { $sum: "$insuranceFee" },
        },
      },
    ]);

    // Extract previous week data
    const previousInsuredWallets = previous7DaysData[0]?.insuredWallets || 0;
    const previousTotalCoverageAmount =
      previous7DaysData[0]?.totalCoverageAmount || 0;
    const previousTotalRevenue = previous7DaysData[0]?.totalRevenue || 0;

    // **✅ Fixed Percentage Calculation**
    const calculatePercentageChange = (current, previous) => {
      if (previous === 0) return current > 0 ? 100 : 0; // Fixed edge case
      const percentage = ((current - previous) / previous) * 100;
      return Math.min(Math.max(percentage, -100), 100); // Ensuring range -100 to +100
    };

    // Calculate percentage changes
    const insuredWalletsPercentage = calculatePercentageChange(
      totalInsuredWallets,
      previousInsuredWallets
    );
    const totalCoverageAmountPercentage = calculatePercentageChange(
      totalCoverageAmount,
      previousTotalCoverageAmount
    );
    const totalRevenuePercentage = calculatePercentageChange(
      totalRevenue,
      previousTotalRevenue
    );

    // Send response
    res.status(200).json({
      insuredWallets: totalInsuredWallets,
      insuredWalletsLast7Days: last7DaysArray.map((day) => day.insuredWallets),
      totalCoverageAmount,
      totalCoverageAmountLast7Days: last7DaysArray.map(
        (day) => day.totalCoverageAmount
      ),
      totalRevenue,
      totalRevenueLast7Days: last7DaysArray.map((day) => day.totalRevenue),
      insuredWalletsPercentage: `${
        insuredWalletsPercentage >= 0 ? "+" : ""
      }${insuredWalletsPercentage.toFixed(2)}%`,
      totalCoverageAmountPercentage: `${
        totalCoverageAmountPercentage >= 0 ? "+" : ""
      }${totalCoverageAmountPercentage.toFixed(2)}%`,
      totalRevenuePercentage: `${
        totalRevenuePercentage >= 0 ? "+" : ""
      }${totalRevenuePercentage.toFixed(2)}%`,
      insurance,
      currentPage,
      totalPages,
    });
  } catch (error) {
    console.error("Error fetching insured users:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

const getInsuredUsers = asyncHandler(async (req, res) => {
  try {
    const {
      subscriptionType,
      minInsuredAmount,
      maxInsuredAmount,
      minInsuranceFee,
      maxInsuranceFee,
      search,
      page = 1,
      limit = 10,
    } = req.query;

    const filter = { paid: true };

    if (subscriptionType) {
      filter.paymentFrequency = subscriptionType;
    }
    if (minInsuredAmount) {
      filter.insuranceCoverageAmount = {
        ...filter.insuranceCoverageAmount,
        $gte: Number(minInsuredAmount),
      };
    }
    if (maxInsuredAmount) {
      filter.insuranceCoverageAmount = {
        ...filter.insuranceCoverageAmount,
        $lte: Number(maxInsuredAmount),
      };
    }
    if (minInsuranceFee) {
      filter.insuranceFee = {
        ...filter.insuranceFee,
        $gte: Number(minInsuranceFee),
      };
    }
    if (maxInsuranceFee) {
      filter.insuranceFee = {
        ...filter.insuranceFee,
        $lte: Number(maxInsuranceFee),
      };
    }
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: "i" } },
        { email: { $regex: search, $options: "i" } },
        { walletAddress: { $regex: search, $options: "i" } },
      ];
    }

    const totalCount = await Insurance.countDocuments(filter);
    const totalStats = await Insurance.aggregate([
      { $match: filter },
      {
        $group: {
          _id: null,
          totalInsuredAmount: { $sum: "$insuranceCoverageAmount" },
          totalRevenue: { $sum: "$insuranceFee" },
        },
      },
    ]);

    const totalInsuredAmount = totalStats[0]?.totalInsuredAmount || 0;
    const totalRevenue = totalStats[0]?.totalRevenue || 0;

    const skip = (page - 1) * limit;
    const insurance = await Insurance.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number(limit));

    res.status(200).json({
      insuredWallets: totalCount,
      totalCoverageAmount: totalInsuredAmount,
      totalRevenue: totalRevenue,
      insurance,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: Number(page),
    });
  } catch (error) {
    console.error("Error fetching insured users:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

const getInsuranceHistory = asyncHandler(async (req, res) => {
  const { walletAddress } = req.params;

  const insuranceHistory = await InsuranceHistory.find({
    insuranceWalletAddress: walletAddress,
  }).populate({
    path: "insuranceId",
    select: "txHash",
  });

  // Build custom response without nested insuranceId object
  const response = insuranceHistory.map((record) => {
    const obj = record.toObject();
    const txHash = obj.insuranceId?.txHash || null;
    delete obj.insuranceId; // Remove insuranceId
    return { ...obj, txHash };
  });

  res.status(200).json({ insuranceHistory: response });
});

const feePercentageOnAmount = asyncHandler(async (req, res) => {
  try {
    const { amount } = req.body;

    if (!amount) {
      return res.status(400).json({ message: "Amount is required" });
    }

    const feePercentage = await FeePercentage.findOne();

    if (!feePercentage) {
      return res.status(404).json({ message: "Fee percentage not found" });
    }

    const calculatedFee = (amount * feePercentage.feePercentage) / 100;

    res.status(200).json({
      amount,
      FeeOnAmount: calculatedFee,
      FeePercentage: `${feePercentage.feePercentage}%`,
    });
  } catch (error) {
    console.error("Error calculating fee percentage:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

const getFeePercentage = asyncHandler(async (req, res) => {
  const feePercentage = await FeePercentage.find();
  res.status(200).json(feePercentage);
});

const getReturnEnvKeys = asyncHandler(async (req, res) => {
  try {
    const API_KEY = req.headers["api-key"];
    if (!API_KEY) {
      return res.status(401).json({ message: "API key is required" });
    }

    const API_KEY_ENV = process.env.API_KEY_SERVER;
    if (API_KEY !== API_KEY_ENV) {
      return res.status(403).json({ message: "API key is incorrect" });
    }
    const RPC_URL = process.env.RPC_URL;
    const BIRDEYE_API_KEY = process.env.BIRDEYE_API_KEY;
    const PROVIDER_QUICKNODE_URL = process.env.ENVPrividerURL;
    const MORALIS_API_KEY = process.env.MORALIS_API_KEY;
    res.status(200).json({
      RPC_URL,
      BIRDEYE_API_KEY,
      PROVIDER_QUICKNODE_URL,
      MORALIS_API_KEY,
    });
  } catch (error) {
    console.error("Error fetching environment keys:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

const getUsersTransactionsDetails = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { search, page = 1, limit = 10 } = req.query;
  if (!userId) {
    return res.status(401).json({ message: "Admin token missing" });
  }

  const filter = { paid: true };

  if (search) {
    filter.$or = [
      { name: { $regex: search, $options: "i" } },
      { email: { $regex: search, $options: "i" } },
      { walletAddress: { $regex: search, $options: "i" } },
    ];
  }

  const skip = (page - 1) * limit;

  const paymentDetails = await Insurance.find(filter)
    .sort({ createdAt: -1 })
    .select(
      "name email walletAddress insuranceCoverageAmount paymentFrequency insuranceFee"
    )
    .skip(skip)
    .limit(Number(limit));

  const totalCount = await Insurance.countDocuments(filter);

  const formattedPaymentDetails = paymentDetails.map((detail) => ({
    name: detail.name,
    email: detail.email,
    walletAddress: detail.walletAddress,
    InsuredAmount: detail.insuranceCoverageAmount,
    Subscription: detail.paymentFrequency,
    insuranceFee: detail.insuranceFee,
  }));

  res.status(200).json({
    PaymentDetails: formattedPaymentDetails,
    totalCount,
    currentPage: Number(page),
    totalPages: Math.ceil(totalCount / limit),
  });
});

const getInsuranceByWalletAddress = asyncHandler(async (req, res) => {
  try {
    const { walletAddress } = req.params; // Get wallet address from URL parameters

    if (!walletAddress) {
      return res.status(400).json({ message: "Wallet address is required" });
    }

    const insurance = await Insurance.findOne({ walletAddress })
      .select(
        "name email walletAddress insuranceCoverageAmount paymentFrequency insuranceFee"
      )
      .lean();

    if (!insurance) {
      return res.status(404).json({ message: "Insurance record not found" });
    }

    // Format the response
    const response = {
      name: insurance.name,
      email: insurance.email,
      walletAddress: insurance.walletAddress,
      InsuredAmount: insurance.insuranceCoverageAmount,
      Subscription: insurance.paymentFrequency,
      insuranceFee: insurance.insuranceFee,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error("Error fetching insurance by wallet address:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

const usersPaymentTracking = asyncHandler(async (req, res) => {
  try {
    if (!req.user || !req.user.id) {
      return res
        .status(401)
        .json({ message: "Unauthorized: Missing or invalid token" });
    }

    // Extract query parameters
    const { paymentFrequency, fromDate, toDate, page, limit } = req.query;
    const pageNum = parseInt(page) || 1;
    const limitNum = parseInt(limit) || 10;
    const skip = (pageNum - 1) * limitNum;

    // Fetch all records first (without filters)
    let insuranceUsers = await Insurance.find({ paid: true })
      .sort({ createdAt: -1 })
      .lean();

    // Apply filtering after fetching data
    let filteredInsurance = insuranceUsers;

    // Filter by multiple payment frequencies
    if (paymentFrequency) {
      const paymentArray = paymentFrequency.split(","); // Convert to array
      filteredInsurance = filteredInsurance.filter((user) =>
        paymentArray.includes(user.paymentFrequency)
      );
    }

    // Filter by createdAt (fromDate to toDate)
    if (fromDate) {
      filteredInsurance = filteredInsurance.filter(
        (user) => new Date(user.createdAt) >= new Date(fromDate)
      );
    }
    if (toDate) {
      filteredInsurance = filteredInsurance.filter(
        (user) => new Date(user.createdAt) <= new Date(toDate)
      );
    }

    // Apply pagination on filtered data
    const totalFilteredRecords = filteredInsurance.length;
    const paginatedInsurance = filteredInsurance.slice(skip, skip + limitNum);

    // Format response
    const formattedInsurance = paginatedInsurance.map((user) => ({
      id: user._id,
      name: user.name,
      email: user.email,
      walletAddress: user.walletAddress,
      insuranceCoverageAmount: user.insuranceCoverageAmount,
      paymentFrequency: user.paymentFrequency,
      createdAt: new Date(user.createdAt),
      date: new Date(user.createdAt).toLocaleDateString(),
      time: new Date(user.createdAt).toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      }),
      insuranceExpiryDate: user.insuranceExpiryDate,
      status:
        new Date(user.insuranceExpiryDate) < new Date() ? "Expired" : "Ongoing",
    }));

    res.status(200).json({
      insurance: formattedInsurance,
      totalFilteredRecords,
      pagination: {
        totalItems: totalFilteredRecords,
        totalPages: Math.ceil(totalFilteredRecords / limitNum),
        currentPage: pageNum,
        limit: limitNum,
      },
    });
  } catch (error) {
    console.error("Error fetching insurance:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

const getPaymentTackById = asyncHandler(async (req, res) => {
  try {
    if (!req.user || !req.user.id) {
      return res
        .status(401)
        .json({ message: "Unauthorized: Missing or invalid token" });
    }
    const { id } = req.params;
    const user = await Insurance.findById(id).lean();

    if (!user) {
      return res.status(404).json({ error: "Insurance record not found" });
    }

    const response = {
      id: user._id,
      category: "Transfer(Send)",
      walletAddress: user.walletAddress,
      amout: user.insuranceCoverageAmount,
      subscripton: user.paymentFrequency,
      date: new Date(user.createdAt).toLocaleDateString(),
      time: new Date(user.createdAt).toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      }),
      plateForm: "Gaya",
      insuranceExpiryDate: user.insuranceExpiryDate,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error("Error fetching insurance by ID:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

const getInsuredUsersByWalletAddress = asyncHandler(async (req, res) => {
  try {
    const { walletAddress } = req.params;

    if (!walletAddress) {
      return res.status(400).json({ message: "Wallet address is required" });
    }

    const insurance = await Insurance.findOne({ walletAddress });

    if (!insurance) {
      return res.status(404).json({ message: "Insurance record not found" });
    }

    res.status(200).json(insurance);
  } catch (error) {
    console.error("Error fetching insurance by wallet address:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

module.exports = {
  createInsurance,
  getInsurance,
  updatePaymentStatus,
  getInsuranceById,
  getDashboardInsuredUsers,
  getInsuredUsers,
  getInsuranceHistory,
  getFeePercentage,
  feePercentageOnAmount,
  getReturnEnvKeys,
  getUsersTransactionsDetails,
  usersPaymentTracking,
  getPaymentTackById,
  getInsuranceByWalletAddress,
  getInsuredUsersByWalletAddress,
};
