const mongoose = require("mongoose");
const DCASchema = new mongoose.Schema(
  {
    walletAddress: String,
    inputTokenAddress: String,
    outputTokenAddress: String,
    category: {
      type: String,
      enum: ["time", "price"],
    },
    priceInterval: String,
    amount: Number,
    allocationCurrency: String,
    currency: String,
    totalDurationHours: Number,
    parts: Number,
    action: String,
    completedParts: { type: Number },
    status: { type: String, default: "active" },
    startTime: String,
    amountPerPart: Number,
    executionTimes: [String],
    minPrice: Number,
    maxPrice: Number,
    lastExecutionTime: String,
    totalDuration: Number,
    durationUnit: String,
    portfolioIncreaseAmount: Number,
    outPutDecimals:String,
    inputDecimals:String,
    outPutSymbol:String,
    inputSymbol:String,
  },
  { timestamps: true }
);
module.exports = mongoose.model("DCA", DCASchema);
