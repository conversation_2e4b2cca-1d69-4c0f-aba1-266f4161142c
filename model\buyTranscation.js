// models/tokenSwap.js
const mongoose = require("mongoose");

const buyTransactionSchema = new mongoose.Schema({
  walletAddress: String,
  amount: Number,
  name: String,
  currencyBought: String,
  fiatCurrency: String,
  amountReceived: Number,

}, { timestamps: true });

const sellTransactionSchema = new mongoose.Schema({
  walletAddress: String,
  sellAmount: String,
  name: String,
  amount: Number,
  currencySold: String,
  fiatCurrency: String,
  amountSold: Number,
}, { timestamps: true });

const BuyTransaction = mongoose.model("BuyTransaction", buyTransactionSchema);
const SellTransaction = mongoose.model(
  "SellTransaction",
  sellTransactionSchema
);

module.exports = { BuyTransaction, SellTransaction };
