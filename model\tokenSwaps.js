const mongoose = require("mongoose");
const tokenSwapSchema = new mongoose.Schema(
  {
    walletAddress: {
      type: String,
    },
    inputToken: {
      type: String,
    },
    outputToken: {
      type: String,
    },
    inputAmount: {
      type: Number,
    },
    transactionHash: {
      type: String,
    },
  },
  { timestamps: true }
);

const TokenSwap = mongoose.model("TokenSwapData", tokenSwapSchema);
module.exports = TokenSwap;
