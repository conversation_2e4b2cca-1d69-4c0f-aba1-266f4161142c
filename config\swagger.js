const swaggerAutogen = require("swagger-autogen")();

const doc = {
  info: {
    title: "Your API Title",
    description: "API documentation description",
  },
  host: "localhost:7000",
  schemes: ["http"],
};

const outputFile = "./config/swagger-output.json";
const endpointsFiles = [
  // "../routes/admin.js",
  // "../routes/insurance.js",
  // "../routes/paymentTrackingRoutes.js",
  // "../routes/transakRoutes.js",
  // "../routes/wallet.js",
  "../routes/confirmPaymentAddress.js",
  "../routes/ethSwap.js",
  "../routes/dca.js",
  "../routes/tokenSwapsRoute.js",
  "../routes/changellyRoutes.js",
  "../routes/userRoutes.js",
  "../routes/taskRoutes.js",
  "../routes/pointsRoutes.js",
  "../routes/contractAddress.js",
];

// swaggerAutogen(outputFile, endpointsFiles, doc).then(() => {
//   require("../index");
// });
