const { TransactionInstruction, PublicKey, AddressLookupTableAccount } = require('@solana/web3.js'); // Import necessary Solana libraries

/**
 * Converts an instruction object into a Solana TransactionInstruction.
 * @param {Instruction | undefined} instruction - The instruction to convert.
 * @returns {TransactionInstruction | null} - The converted transaction instruction or null if the input is invalid.
 */
function instructionDataToTransactionInstruction(instruction) {
  if (instruction === null || instruction === undefined) return null;

  return new TransactionInstruction({
    programId: new PublicKey(instruction.programId),
    keys: instruction.accounts.map((key) => ({
      pubkey: new PublicKey(key.pubkey),
      isSigner: key.isSigner,
      isWritable: key.isWritable,
    })),
    data: Buffer.from(instruction.data, 'base64'),
  });
}
async function getAdressLookupTableAccounts(
  keys,
  connection,
) {
  const addressLookupTableAccountInfos = await connection.getMultipleAccountsInfo(
    keys.map((key) => new PublicKey(key)),
  );

  return addressLookupTableAccountInfos.reduce((acc, accountInfo, index) => {
    const addressLookupTableAddress = keys[index];
    if (accountInfo) {
      const addressLookupTableAccount = new AddressLookupTableAccount({
        key: new PublicKey(addressLookupTableAddress),
        state: AddressLookupTableAccount.deserialize(accountInfo.data),
      });
      acc.push(addressLookupTableAccount);
    }
    return acc;
  }, []);

}

const paginate = (items, pageSize, page) => {
  const totalItems = items.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedItems = items.slice(startIndex, endIndex);

  return {
    paginatedItems,
    totalPages,
    currentPage: page,
    totalItems,
  };
};



// Export the function
module.exports = { instructionDataToTransactionInstruction,getAdressLookupTableAccounts,paginate};