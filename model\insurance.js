const mongoose = require("mongoose");

const insuranceSchema = new mongoose.Schema(
  {
    walletAddress: {
      type: String,
    },
    name: {
      type: String,
    },
    email: {
      type: String,
    },
    insuranceCoverageAmount: {
      type: Number,
    },
    nationality: {
      type: String,
    },
    nationalId: {
      type: String,
    },
    paymentFrequency: {
      type: String,
      enum: ["monthly", "yearly"],
    },
    cardType: {
      type: String,
    },
    cardExpiryDate: {
      type: String,
    },
    cvv: {
      type: Number,
    },
    preferredType: {
      type: String,
    },
    paid: {
      type: Boolean,
      default: false,
    },
    txHash: {
      type: String,
      default: "",
    },
    insuranceFee: {
      type: Number,
      default: 0,
    },
    insuranceExpiryDate: {
      type: Date,
    },
    newlyCreated: {
      type: Boolean,
      default: true,
    },
  },
  { timestamps: true }
);

const Insurance = mongoose.model("Insurance", insuranceSchema);

module.exports = Insurance;
