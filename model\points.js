const mongoose = require("mongoose");

const pointsSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    walletAddress: {
      type: String,
    },
    refrenceId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Task",
    },
    orderId: {
      type: String,
    },
    pointsType: {
      type: String,
      enum: ["refer", "login", "task", "bonus", "buy", "sell", "dca"],
      required: true,
    },
    points: {
      type: Number,
      required: true,
      min: 0,
    },
    description: {
      type: String,
      default: "",
    },
    metadata: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
  },
  { timestamps: true }
);

const Points = mongoose.model("Points", pointsSchema);
module.exports = Points;
