const express = require('express');
const { ethSwap,executeSwap,executeSwapSell,tokenList,getSignData,swapTokenUniversal } = require('../controller/ethSwap');

const router = express.Router();

router.post("/swap", ethSwap);

router.post("/swap-execute", swapTokenUniversal);
router.post("/getSignedData", getSignData);

// router.post("/swap", getSignData);

router.get("/token-list", tokenList);


module.exports = router;