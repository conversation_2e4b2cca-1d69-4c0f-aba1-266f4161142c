const Admin = require("../model/admin");
const jwt = require("jsonwebtoken");
const asyncHandler = require("express-async-handler");

//admin login
exports.adminLogin = asyncHandler(async (req, res) => {
  const { email, password } = req.body;
  if (!email || !password) {
    return res.status(400).json({ msg: "Please enter all fields" });
  }

  try {
    const admin = await Admin.findOne({ email });
    
    if (!admin) {
      return res.status(400).json({ msg: "Admin not found" });
    }

    // Directly compare the plain text password
    if (password !== admin.password) {
      return res.status(400).json({ msg: "Invalid Admin Credentials." });
    }

    // Generate JWT token
    const token = jwt.sign({ id: admin._id }, process.env.JWT_SECRET);

    return res.status(200).json({
      msg: "Admin logged in successfully",
      token,
      admin
    });
  } catch (error) {
    return res
      .status(500)
      .json({ msg: "Error admin login", error: error.message });
  }
});


