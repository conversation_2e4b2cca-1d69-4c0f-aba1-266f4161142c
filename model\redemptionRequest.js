const mongoose = require('mongoose');

const redemptionRequestSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    // required: true,
  },
  solanaWalletAddress: {
    type: String,
    // required: true,
  },
  
  rank: {
    type: String,
    enum: ['bronze', 'silver', 'gold'],
    // required: true,
  },
  pointsRedeemed: {
    type: Number,
    // required: true,
  },
  swapAmount: {
    type: Number,
    // required: true,
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending',
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

module.exports = mongoose.model('RedemptionRequest', redemptionRequestSchema); 