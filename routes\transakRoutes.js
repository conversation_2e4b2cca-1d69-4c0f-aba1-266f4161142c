const express = require("express");
const router = express.Router();

const {
  initiatePayment,
  initiateSELL,
  getFiatCurrencies,
  getCryptoCurrencies,
  createCryptoToFiatTransaction,
  getMoonPayQuote,
} = require("../controller/transak");

router.post("/create-transaction", initiatePayment);
router.post("/initiate-sell", initiateSELL);
router.get("/get-currencies", getFiatCurrencies);
router.get("/get-crypto-currencies", getCryptoCurrencies);
router.post("/create-sell-transaction", createCryptoToFiatTransaction);
router.get("/get-moonpay-quote", getMoonPayQuote);

module.exports = router;
