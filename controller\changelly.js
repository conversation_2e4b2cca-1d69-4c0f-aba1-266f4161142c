const crypto = require("crypto");
const User = require("../model/user");
const Points = require("../model/points");
const Task = require("../model/task");
const request = require("axios");
const axios = require("axios");
const dotenv = require("dotenv");
const orderHistory = require("../model/orderHistory");
dotenv.config();

const API_PUBLIC_KEY =
  "fbcbea8542f0187c4103913965951134cdd773d450cb753e7e7083a2e4546603";
const API_PRIVATE_KEY = process.env.CHANGELLY_PRIVATE_KEY;
module.exports = {
  getproviders: async (req, res) => {
    const privateKey = crypto.createPrivateKey({
      key: API_PRIVATE_KEY,
      type: "pkcs1",
      format: "pem",
      encoding: "base64",
    });
    const path = "https://fiat-api.changelly.com/v1/providers";
    const message = {};

    const payload = path + JSON.stringify(message);

    const signature = crypto
      .sign("sha256", Buffer.from(payload), privateKey)
      .toString("base64");
    const options = {
      method: "GET",
      url: path,
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": API_PUBLIC_KEY,
        "X-Api-Signature": signature,
      },
      body: JSON.stringify(message),
    };

    const { data } = await request(options);
    res.json({ statusCode: 200, message: "Provider List", data: data });
  },
  getCureencyList: async (req, res) => {
    try {
      const { type, providerCode, currency } = req.query;

      const privateKey = crypto.createPrivateKey({
        key: API_PRIVATE_KEY,
        type: "pkcs1",
        format: "pem",
        encoding: "base64",
      });

      let path = `https://fiat-api.changelly.com/v1/currencies`;
      const queryParams = [];
      if (providerCode)
        queryParams.push(`providerCode=${encodeURIComponent(providerCode)}`);
      if (type) queryParams.push(`type=${encodeURIComponent(type)}`);
      if (queryParams.length > 0) path += `?${queryParams.join("&")}`;

      const message = {};
      const payload = path + JSON.stringify(message);

      const signature = crypto
        .sign("sha256", Buffer.from(payload), privateKey)
        .toString("base64");

      const options = {
        method: "GET",
        url: path,
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": API_PUBLIC_KEY,
          "X-Api-Signature": signature,
        },
        body: JSON.stringify(message),
      };

      const { data } = await request(options);

      const filteredData = currency
        ? data.find(
            (item) => item.ticker?.toLowerCase() === currency.toLowerCase()
          )
        : data;

      if (currency && !filteredData) {
        return res.status(404).json({
          statusCode: 404,
          message: `Currency '${currency}' not found`,
        });
      }

      const responseData = currency
        ? {
            ticker: filteredData.ticker,
            name: filteredData.name,
            type: filteredData.type,
            network: filteredData.network,
            protocol: filteredData.protocol,
            providers: filteredData.providers.map((p) => ({
              providerCode: p.providerCode,
              limits: p.limits,
            })),
          }
        : data;

      return res.status(200).json({
        statusCode: 200,
        message: currency
          ? `Details for currency: ${currency}`
          : "Currency List",
        data: responseData,
      });
    } catch (error) {
      console.error("Error fetching currency list:", error.message);
      return res.status(500).json({
        statusCode: 500,
        message: "Internal Server Error",
      });
    }
  },

  getAvalibleCountry: async (req, res) => {
    try {
      const { providerCode } = req.query;
      const privateKey = crypto.createPrivateKey({
        key: API_PRIVATE_KEY,
        type: "pkcs1",
        format: "pem",
        encoding: "base64",
      });
      let path = `https://fiat-api.changelly.com/v1/available-countries`;
      if (providerCode) {
        path += `?providerCode=${encodeURIComponent(providerCode)}`;
      }
      const message = {};

      const payload = path + JSON.stringify(message);

      const signature = crypto
        .sign("sha256", Buffer.from(payload), privateKey)
        .toString("base64");
      const options = {
        method: "GET",
        url: path,
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": API_PUBLIC_KEY,
          "X-Api-Signature": signature,
        },
        body: JSON.stringify(message),
      };
      const { data } = await request(options);
      res.json({
        statusCode: 200,
        message: "Country Availablity List",
        data: data,
      });
    } catch (e) {
      res.status(500).json(e);
    }
  },
  // getOffers: async (req, res) => {
  //   try {
  //     const {
  //       providerCode,
  //       externalUserId,
  //       currencyFrom,
  //       currencyTo,
  //       amountFrom,
  //       country,
  //       state,
  //       ip,
  //     } = req.query;

  //     const privateKey = crypto.createPrivateKey({
  //       key: API_PRIVATE_KEY,
  //       type: "pkcs1",
  //       format: "pem",
  //       encoding: "base64",
  //     });

  //     let path = `https://fiat-api.changelly.com/v1/offers?currencyFrom=${currencyFrom}&currencyTo=${currencyTo}&amountFrom=${amountFrom}&country=${country}`;

  //     if (ip) {
  //       path += `&ip=${encodeURIComponent(ip)}`;
  //     }
  //     if (providerCode) {
  //       path += `&providerCode=${encodeURIComponent(providerCode)}`;
  //     }
  //     if (state) {
  //       path += `&state=${encodeURIComponent(state)}`;
  //     }
  //     if (externalUserId) {
  //       path += `&externalUserId=${encodeURIComponent(externalUserId)}`;
  //     }

  //     console.log(path);

  //     const message = {
  //       currencyFrom,
  //       currencyTo,
  //       amountFrom,
  //       country,
  //       state,
  //     };

  //     const payload = path + JSON.stringify(message);

  //     console.log("i am message", message);

  //     const signature = crypto
  //       .sign("sha256", Buffer.from(payload), privateKey)
  //       .toString("base64");

  //     const options = {
  //       method: "GET",
  //       url: path,
  //       headers: {
  //         "Content-Type": "application/json",
  //         "X-Api-Key": API_PUBLIC_KEY,
  //         "X-Api-Signature": signature,
  //       },
  //       data: message,
  //     };
  //     const response = await axios(options);

  //     res.json({
  //       statusCode: 200,
  //       message: "Get Offer",
  //       data: response.data,
  //     });
  //   } catch (ex) {
  //     res.json(ex);
  //   }
  // },

  createOrderNew: async (req, res) => {
    try {
      const {
        currencyFrom,
        currencyTo,
        amountFrom,
        country,
        state,
        walletExtraId,
        paymentMethod,
        userAgent,
        metadata,
        walletAddress,
        ip,
      } = req.body;

      // Step: get currency details to check buy support
      const currencyDetails = await getCurrencyDetails(currencyTo);

      if (!currencyDetails || !currencyDetails.providers) {
        return res.status(404).json({
          statusCode: 404,
          message: "Currency or providers not found",
        });
      }

      // Filter providers that support 'buy' flow
      const buyProviders = currencyDetails.providers.filter(
        (provider) =>
          provider.supportedFlows && provider.supportedFlows.includes("buy")
      );

      if (buyProviders.length === 0) {
        return res.status(400).json({
          statusCode: 400,
          message: "No providers support buying this currency",
        });
      }

      // Creating private key using provided format and encoding
      const privateKey = crypto.createPrivateKey({
        key: API_PRIVATE_KEY,
        type: "pkcs1",
        format: "pem",
        encoding: "base64",
      });

      const results = [];
      const dynamicUserId = "user-" + Date.now();

      // Generate order for each provider that supports 'buy'
      for (const provider of buyProviders) {
        try {
          const dynamicOrderId = `order-${provider.providerCode}-${Date.now()}`;
          let path = "https://fiat-api.changelly.com/v1/orders";

          const message = {
            providerCode: provider.providerCode,
            externalUserId: dynamicUserId,
            currencyFrom,
            currencyTo,
            amountFrom,
            country,
            externalOrderId: dynamicOrderId,
            state,
            walletExtraId,
            paymentMethod,
            userAgent,
            metadata,
            walletAddress,
            ip,
          };

          const payload = path + JSON.stringify(message);
          const signature = crypto
            .sign("sha256", Buffer.from(payload), privateKey)
            .toString("base64");

          const options = {
            method: "POST",
            url: path,
            headers: {
              "Content-Type": "application/json",
              "X-Api-Key": API_PUBLIC_KEY,
              "X-Api-Signature": signature,
            },
            data: message,
          };

          const response = await axios(options);
          console.log("this is response", response.data);
          results.push({
            providerCode: provider.providerCode,
            orderData: response.data,
          });
        } catch (providerError) {
          console.error(
            `Error with provider ${provider.providerCode}:`,
            providerError.message
          );
          results.push({
            providerCode: provider.providerCode,
            error:
              providerError.message ||
              "Failed to create order with this provider",
          });
        }
      }

      return res.json({
        statusCode: 200,
        message: "Created orders with supported providers",
        data: results,
      });
    } catch (error) {
      console.error("Error:", error);
      return res.status(error.response?.status || 500).json({
        statusCode: error.response?.status || 500,
        message: error.message || "An error occurred",
      });
    }
  },

  getOffers: async (req, res) => {
    try {
      const type = req.query.type;
      // console.log("i am type", type);

      if (!type || (type === "buy" && type === "sell")) {
        return res.status(400).json({
          statusCode: 400,
          message: "Invalid type parameter. Use 'buy' or 'sell'.",
        });
      }

      const { externalUserId, currencyFrom, currencyTo, amountFrom, country } =
        req.body;

      // Validate required fields
      if (
        // !externalUserId ||
        !currencyFrom ||
        !currencyTo ||
        !amountFrom ||
        !country
      ) {
        return res.status(400).json({
          statusCode: 400,
          message: "Missing required query parameters",
        });
      }

      // Construct path with query parameters
      let path;
      if (type === "buy") {
        path = `https://fiat-api.changelly.com/v1/offers`;
      } else {
        path = `https://fiat-api.changelly.com/v1/sell/offers`;
      }
      const queryParams = [
        // `externalUserId=${encodeURIComponent(externalUserId)}`,
        `currencyFrom=${encodeURIComponent(currencyFrom)}`,
        `currencyTo=${encodeURIComponent(currencyTo)}`,
        `amountFrom=${encodeURIComponent(amountFrom)}`,
        `country=${encodeURIComponent(country)}`,
      ];
      path += `?${queryParams.join("&")}`;

      const privateKey = crypto.createPrivateKey({
        key: API_PRIVATE_KEY,
        type: "pkcs1",
        format: "pem",
        encoding: "base64",
      });

      const message = {}; // No body for GET, but consistent with your signing logic
      const payload = path + JSON.stringify(message);

      const signature = crypto
        .sign("sha256", Buffer.from(payload), privateKey)
        .toString("base64");

      const options = {
        method: "GET",
        url: path,
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": API_PUBLIC_KEY,
          "X-Api-Signature": signature,
        },
      };

      const { data } = await request(options);

      return res.status(200).json({
        statusCode: 200,
        message: "Offers fetched successfully",
        data,
      });
    } catch (error) {
      console.error("Error fetching offers:", error.message);
      return res.status(error.response?.status || 500).json({
        statusCode: error.response?.status || 500,
        message: error.message || "Failed to fetch offers",
      });
    }
  },

  createOrderPc: async (req, res) => {
    try {
      const {
        providerCode,
        // externalUserId,
        currencyFrom,
        currencyTo,
        amountFrom,
        country,
        // externalOrderId,
        state,
        walletExtraId,
        paymentMethod,
        userAgent,
        metadata,
        walletAddress,
        ip,
      } = req.body;
      const dynamicOrderId = `order-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 8)}`;
      const dynamicUserId = `user-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 8)}`;
      // let paymentCode = "moonpay";
      // if (currencyTo === "USDTSOL") {
      //   paymentCode = "transak";
      // }
      console.log(currencyTo, providerCode);
      // Creating private key using provided format and encoding
      const privateKey = crypto.createPrivateKey({
        key: API_PRIVATE_KEY,
        type: "pkcs1",
        format: "pem",
        encoding: "base64",
      });
      let path = "https://fiat-api.changelly.com/v1/orders";
      const message = {
        providerCode: providerCode,
        externalUserId: dynamicUserId,
        currencyFrom,
        currencyTo,
        amountFrom,
        country,
        externalOrderId: dynamicOrderId,
        state,
        walletExtraId,
        paymentMethod,
        userAgent,
        metadata,
        walletAddress,
        ip,
      };
      const payload = path + JSON.stringify(message);
      const signature = crypto
        .sign("sha256", Buffer.from(payload), privateKey)
        .toString("base64");
      console.log("Payload: ", payload);
      console.log("Signature: ", signature);
      const options = {
        method: "POST",
        url: path,
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": API_PUBLIC_KEY,
          "X-Api-Signature": signature,
        },
        data: message,
      };
      const response = await axios(options);
      await orderHistory.create({
        externalOrderId: dynamicOrderId,
        providerCode: providerCode,
        orderType: "buy",
        currencyFrom,
        currencyTo,
        amountFrom: parseFloat(amountFrom),
        status: response.data.status || "pending",
        paymentUrl: response.data.paymentUrl || null,
      });

      // Award points for purchase
      try {
        if (!walletAddress) {
          console.warn("⚠️ walletAddress not provided in request");
        } else {
          const normalizedWallet = walletAddress.trim().toLowerCase();
          const user = await User.findOne({
            $or: [
              { "walletAddress.ethWalletAddress": normalizedWallet },
              { "walletAddress.solanaWalletAddress": normalizedWallet },
            ],
          });

          if (user) {
            const alreadyRewarded = await Points.findOne({
              orderId: dynamicOrderId,
            });

            if (alreadyRewarded) {
              console.log(
                `⏩ Skipped awarding points: order ${dynamicOrderId} already rewarded`
              );
            } else {
              const purchaseAmount = parseFloat(amountFrom) || 0;

              const task = await Task.findOne({
                category: "crypto",
                type: "buy",
              });

              if (task && task.requirements && task.points) {
                const requirement = task.requirements;
                const pointPerBlock = task.points;

                const pointsToAward =
                  purchaseAmount >= requirement
                    ? Math.floor(purchaseAmount / requirement) * pointPerBlock
                    : 0;

                console.log(
                  `💰 Purchase: ${purchaseAmount}, Requirement: ${requirement}, Points/block: ${pointPerBlock}, Awarding: ${pointsToAward}`
                );

                if (pointsToAward > 0) {
                  await Points.create({
                    walletAddress:
                      user.walletAddress.ethWalletAddress ||
                      user.walletAddress.solanaWalletAddress,
                    pointsType: "buy",
                    points: pointsToAward,
                    orderId: dynamicOrderId,
                    metadata: { purchaseAmount },
                  });

                  user.totalPoints = (user.totalPoints || 0) + pointsToAward;
                  await user.save();

                  console.log(
                    `🎯 Awarded ${pointsToAward} points to wallet ${walletAddress} for order ${dynamicOrderId}`
                  );
                } else {
                  console.log(
                    `⚠️ No points awarded: purchase amount ${purchaseAmount} is below requirement of $${requirement}`
                  );
                }
              } else {
                console.warn(
                  "⚠️ Task config not found or incomplete for 'Buy'"
                );
              }
            }
          } else {
            console.warn("⚠️ Skipped awarding: user not found", {
              walletAddress: normalizedWallet,
            });
          }
        }
      } catch (pointsError) {
        console.error("🔥 Error awarding points:", pointsError);
      }

      return res.json({
        statusCode: 200,
        message: "Create Order",
        data: response.data,
      });
    } catch (error) {
      console.error("Error:", error);
      return res.json({
        statusCode: error.response?.status || 500,
        message: error.message || "An error occurred",
      });
    }
  },

  validateWalletAddress: async (req, res) => {
    try {
      const { currency, walletExtraId, walletAddress } = req.body;

      const privateKey = crypto.createPrivateKey({
        key: API_PRIVATE_KEY,
        type: "pkcs1",
        format: "pem",
        encoding: "base64",
      });

      let path = "https://fiat-api.changelly.com/v1/validate-address";
      const message = {
        currency,
        walletExtraId,
        walletAddress,
      };

      const payload = path + JSON.stringify(message);

      const signature = crypto
        .sign("sha256", Buffer.from(payload), privateKey)
        .toString("base64");
      const options = {
        method: "POST",
        url: path,
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": API_PUBLIC_KEY,
          "X-Api-Signature": signature,
        },
        data: message,
      };
      const response = await axios(options);

      res.json({
        statusCode: 200,
        message: "Validate WalletAddress Success",
        data: response.data,
      });
    } catch (ex) {
      res.json(ex);
    }
  },
  sellOrderPc: async (req, res) => {
    try {
      const {
        currencyFrom,
        currencyTo,
        amountFrom,
        country,
        walletAddress,
        providerCode,
        paymentMethod,
      } = req.body;
      const dynamicOrderId = `sell-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 8)}`;
      const dynamicUserId = `user-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 8)}`;
      // const providerCode = "moonpay";
      // const providerCode = "transak";
      const privateKey = crypto.createPrivateKey({
        key: API_PRIVATE_KEY,
        type: "pkcs1",
        format: "pem",
        encoding: "base64",
      });
      const path = "https://fiat-api.changelly.com/v1/sell/orders";
      const message = {
        providerCode,
        externalUserId: dynamicUserId,
        currencyFrom,
        currencyTo,
        amountFrom,
        country,
        externalOrderId: dynamicOrderId,
        refundAddress: walletAddress,
        walletAddress,
        paymentMethod,
      };
      const payload = path + JSON.stringify(message);
      const signature = crypto
        .sign("sha256", Buffer.from(payload), privateKey)
        .toString("base64");
      const options = {
        method: "POST",
        url: path,
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": API_PUBLIC_KEY,
          "X-Api-Signature": signature,
        },
        data: message,
      };
      const response = await axios(options);
      await orderHistory.create({
        externalOrderId: dynamicOrderId,
        providerCode: providerCode,
        orderType: "sell",
        currencyFrom,
        currencyTo,
        amountFrom: parseFloat(amountFrom),
        status: response.data.status || "pending",
        paymentUrl: response.data.paymentUrl || null,
      });

      // Award points for sell orders - 10 points for every 100 currency units
      try {
        if (!walletAddress) {
          console.warn("⚠️ walletAddress not provided in request");
        } else {
          const normalizedWallet = walletAddress.trim().toLowerCase();
          const user = await User.findOne({
            $or: [
              { "walletAddress.ethWalletAddress": normalizedWallet },
              { "walletAddress.solanaWalletAddress": normalizedWallet },
            ],
          });

          if (user) {
            const alreadyRewarded = await Points.findOne({
              orderId: dynamicOrderId,
            });

            if (alreadyRewarded) {
              console.log(
                `⏩ Skipped awarding points: order ${dynamicOrderId} already rewarded`
              );
            } else {
              const sellAmount = parseFloat(amountFrom) || 0;

              const task = await Task.findOne({
                category: "crypto",
                type: "deposit",
              });

              if (task && task.requirements && task.points) {
                const requirement = task.requirements;
                const pointPerBlock = task.points;

                const pointsToAward =
                  sellAmount >= requirement
                    ? Math.floor(sellAmount / requirement) * pointPerBlock
                    : 0;

                console.log(
                  `💰 Sell amount: ${sellAmount}, Requirement: ${requirement}, Points/block: ${pointPerBlock}, Awarding: ${pointsToAward}`
                );

                if (pointsToAward > 0) {
                  await Points.create({
                    walletAddress:
                      user.walletAddress.ethWalletAddress ||
                      user.walletAddress.solanaWalletAddress,
                    pointsType: "sell",
                    points: pointsToAward,
                    orderId: dynamicOrderId,
                    metadata: { sellAmount },
                  });

                  user.totalPoints = (user.totalPoints || 0) + pointsToAward;
                  await user.save();

                  console.log(
                    `🎯 Awarded ${pointsToAward} points to wallet ${walletAddress} for sell order ${dynamicOrderId}`
                  );
                } else {
                  console.log(
                    `⚠️ No points awarded: sell amount ${sellAmount} is below requirement of $${requirement}`
                  );
                }
              } else {
                console.warn(
                  "⚠️ Task config not found or incomplete for 'Sell'"
                );
              }
            }
          } else {
            console.warn("⚠️ Skipped awarding: user not found", {
              walletAddress: normalizedWallet,
            });
          }
        }
      } catch (pointsError) {
        console.error("🔥 Error awarding sell points:", pointsError);
      }

      return res.json({
        statusCode: 200,
        message: "Sell Order Created",
        data: response.data,
      });
    } catch (error) {
      console.error(
        "Sell Error:",
        error.response?.data || error.message || error
      );
      return res.status(error.response?.status || 500).json({
        message: error.response?.data || "Sell order failed",
      });
    }
  },

  sellOrderNew: async (req, res) => {
    try {
      const {
        currencyFrom,
        currencyTo,
        amountFrom,
        country,
        walletAddress,
        state,
        ip,
      } = req.body;

      // Get currency details to check sell support
      const currencyDetails = await getCurrencyDetails(currencyFrom);
      console.log(currencyDetails);

      if (!currencyDetails || !currencyDetails.providers) {
        return res.status(404).json({
          statusCode: 404,
          message: "Currency or providers not found",
        });
      }

      // Filter providers that support 'sell' flow
      const sellProviders = currencyDetails.providers.filter(
        (provider) =>
          provider.supportedFlows && provider.supportedFlows.includes("sell")
      );

      if (sellProviders.length === 0) {
        return res.status(400).json({
          statusCode: 400,
          message: "No providers support selling this currency",
        });
      }

      const privateKey = crypto.createPrivateKey({
        key: API_PRIVATE_KEY,
        type: "pkcs1",
        format: "pem",
        encoding: "base64",
      });

      const results = [];
      const dynamicUserId = "user-" + Date.now();

      // Generate order for each provider that supports 'sell'
      for (const provider of sellProviders) {
        try {
          const dynamicOrderId = `sell-${provider.providerCode}-${Date.now()}`;
          const path = "https://fiat-api.changelly.com/v1/sell/orders";

          const message = {
            providerCode: provider.providerCode,
            externalUserId: dynamicUserId,
            currencyFrom,
            currencyTo,
            amountFrom,
            country,
            externalOrderId: dynamicOrderId,
            refundAddress: walletAddress,
            walletAddress,
          };

          const payload = path + JSON.stringify(message);
          const signature = crypto
            .sign("sha256", Buffer.from(payload), privateKey)
            .toString("base64");

          const options = {
            method: "POST",
            url: path,
            headers: {
              "Content-Type": "application/json",
              "X-Api-Key": API_PUBLIC_KEY,
              "X-Api-Signature": signature,
            },
            data: message,
          };

          const response = await axios(options);
          results.push({
            providerCode: provider.providerCode,
            orderData: response.data,
          });
        } catch (providerError) {
          console.error(
            `Error with provider ${provider.providerCode}:`,
            providerError.message
          );
          results.push({
            providerCode: provider.providerCode,
            error:
              providerError.message ||
              "Failed to create sell order with this provider",
          });
        }
      }

      return res.json({
        statusCode: 200,
        message: "Sell orders created with supported providers",
        data: results,
      });
    } catch (error) {
      console.error(
        "Sell Error:",
        error.response?.data || error.message || error
      );
      return res.status(error.response?.status || 500).json({
        statusCode: error.response?.status || 500,
        message: error.response?.data || "Sell order failed",
      });
    }
  },
  currencyListByNetwork: async (req, res) => {
    try {
      const { type, network } = req.query;

      const privateKey = crypto.createPrivateKey({
        key: API_PRIVATE_KEY,
        type: "pkcs1",
        format: "pem",
        encoding: "base64",
      });

      const path = `https://fiat-api.changelly.com/v1/currencies`;
      const message = {};
      const payload = path + JSON.stringify(message);

      const signature = crypto
        .sign("sha256", Buffer.from(payload), privateKey)
        .toString("base64");

      const options = {
        method: "GET",
        url: path,
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": API_PUBLIC_KEY,
          "X-Api-Signature": signature,
        },
        body: JSON.stringify(message),
      };

      const { data } = await request(options);

      const filteredList = network
        ? data.filter(
            (item) => item.network?.toLowerCase() === network.toLowerCase()
          )
        : [];

      if (!filteredList.length) {
        return res.status(404).json({
          statusCode: 404,
          message: "No matching currencies found for the specified network",
        });
      }

      const processedList = filteredList
        .map((item) => {
          const { ticker, name, network, protocol, providers } = item;

          if (network?.toLowerCase() === "solana") {
            const solanaProviders = providers
              .map((p) => {
                const prices =
                  type === "buy"
                    ? { buy: p.buy }
                    : type === "sell"
                    ? { sell: p.sell }
                    : {};
                return {
                  providerCode: p.providerCode,
                  limits: p.limits,
                  ...prices,
                };
              })
              .filter((p) => (type ? Object.keys(p).includes(type) : true));

            return {
              ticker,
              name,
              network,
              protocol,
              providers: solanaProviders,
            };
          }

          if (network?.toLowerCase() === "ethereum") {
            return {
              ticker,
              name,
              network,
              protocol,
              providers: providers.map((p) => ({
                providerCode: p.providerCode,
                limits: p.limits,
                buy: p.buy,
                sell: p.sell,
              })),
            };
          }

          return null;
        })
        .filter(Boolean);

      return res.status(200).json({
        statusCode: 200,
        message: `Currency list for network: ${network}`,
        data: processedList,
      });
    } catch (error) {
      console.error("Error fetching currency list:", error.message);
      return res.status(500).json({
        statusCode: 500,
        message: "Internal Server Error",
      });
    }
  },
  createOrder: async (req, res) => {
    try {
      const {
        // providerCode,
        // externalUserId,
        currencyFrom,
        currencyTo,
        amountFrom,
        country,
        // externalOrderId,
        state,
        walletExtraId,
        paymentMethod,
        userAgent,
        metadata,
        walletAddress,
        ip,
      } = req.body;
      const dynamicOrderId = `order-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 8)}`;
      const dynamicUserId = `user-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 8)}`;
      let paymentCode = "moonpay";
      if (currencyTo === "USDTSOL") {
        paymentCode = "transak";
      }
      console.log(currencyTo, paymentCode);
      // Creating private key using provided format and encoding
      const privateKey = crypto.createPrivateKey({
        key: API_PRIVATE_KEY,
        type: "pkcs1",
        format: "pem",
        encoding: "base64",
      });
      let path = "https://fiat-api.changelly.com/v1/orders";
      const message = {
        providerCode: paymentCode,
        externalUserId: dynamicUserId,
        currencyFrom,
        currencyTo,
        amountFrom,
        country,
        externalOrderId: dynamicOrderId,
        state,
        walletExtraId,
        paymentMethod,
        userAgent,
        metadata,
        walletAddress,
        ip,
      };
      const payload = path + JSON.stringify(message);
      const signature = crypto
        .sign("sha256", Buffer.from(payload), privateKey)
        .toString("base64");
      console.log("Payload: ", payload);
      console.log("Signature: ", signature);
      const options = {
        method: "POST",
        url: path,
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": API_PUBLIC_KEY,
          "X-Api-Signature": signature,
        },
        data: message,
      };
      const response = await axios(options);
      await orderHistory.create({
        externalOrderId: dynamicOrderId,
        providerCode: paymentCode,
        orderType: "buy",
        currencyFrom,
        currencyTo,
        amountFrom: parseFloat(amountFrom),
        status: response.data.status || "pending",
        paymentUrl: response.data.paymentUrl || null,
      });
      return res.json({
        statusCode: 200,
        message: "Create Order",
        data: response.data,
      });
    } catch (error) {
      console.error("Error:", error);
      return res.json({
        statusCode: error.response?.status || 500,
        message: error.message || "An error occurred",
      });
    }
  },
  sellOrder: async (req, res) => {
    try {
      const { currencyFrom, currencyTo, amountFrom, country, walletAddress } =
        req.body;
      const dynamicOrderId = `order-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 8)}`;
      const dynamicUserId = `user-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 8)}`;
      const providerCode = "moonpay";
      // const providerCode = "transak";
      const privateKey = crypto.createPrivateKey({
        key: API_PRIVATE_KEY,
        type: "pkcs1",
        format: "pem",
        encoding: "base64",
      });
      const path = "https://fiat-api.changelly.com/v1/sell/orders";
      const message = {
        providerCode,
        externalUserId: dynamicUserId,
        currencyFrom,
        currencyTo,
        amountFrom,
        country,
        externalOrderId: dynamicOrderId,
        refundAddress: walletAddress,
        walletAddress,
      };
      const payload = path + JSON.stringify(message);
      const signature = crypto
        .sign("sha256", Buffer.from(payload), privateKey)
        .toString("base64");
      const options = {
        method: "POST",
        url: path,
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": API_PUBLIC_KEY,
          "X-Api-Signature": signature,
        },
        data: message,
      };
      const response = await axios(options);
      await orderHistory.create({
        externalOrderId: dynamicOrderId,
        providerCode: providerCode,
        orderType: "buy",
        currencyFrom,
        currencyTo,
        amountFrom: parseFloat(amountFrom),
        status: response.data.status || "pending",
        paymentUrl: response.data.paymentUrl || null,
      });
      return res.json({
        statusCode: 200,
        message: "Sell Order Created",
        data: response.data,
      });
    } catch (error) {
      console.error(
        "Sell Error:",
        error.response?.data || error.message || error
      );
      return res.status(error.response?.status || 500).json({
        message: error.response?.data || "Sell order failed",
      });
    }
  },

  registerWebhook: async (req, res) => {
    try {
      const { callbackUrl } = req.body;

      if (!callbackUrl) {
        return res.status(400).json({
          statusCode: 400,
          message: "Callback URL is required",
        });
      }

      const privateKey = crypto.createPrivateKey({
        key: API_PRIVATE_KEY,
        type: "pkcs1",
        format: "pem",
        encoding: "base64",
      });

      // Try the updated webhook endpoint path
      const path = "https://fiat-api.changelly.com/v1/webhook"; // Changed from webhooks to webhook
      const message = {
        callbackUrl,
        events: ["order.status.changed", "sell.order.status.changed"],
      };

      const payload = path + JSON.stringify(message);
      const signature = crypto
        .sign("sha256", Buffer.from(payload), privateKey)
        .toString("base64");

      console.log("Registering webhook with URL:", callbackUrl);
      console.log("Using API path:", path);

      const options = {
        method: "POST",
        url: path,
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": API_PUBLIC_KEY,
          "X-Api-Signature": signature,
        },
        data: message,
      };

      const response = await axios(options);

      return res.json({
        statusCode: 200,
        message: "Webhook registered successfully",
        data: response.data,
      });
    } catch (error) {
      console.error("Error registering webhook:", error);
      console.error(
        "Error details:",
        error.response?.data || "No detailed error data"
      );

      // If we get a 404 with the updated path, try the alternative path
      if (
        error.response?.status === 404 &&
        error.config?.url === "https://fiat-api.changelly.com/v1/webhook"
      ) {
        try {
          console.log("Trying alternative webhook registration endpoint...");

          const privateKey = crypto.createPrivateKey({
            key: API_PRIVATE_KEY,
            type: "pkcs1",
            format: "pem",
            encoding: "base64",
          });

          const altPath = "https://fiat-api.changelly.com/v2/webhook";
          const message = {
            callbackUrl: req.body.callbackUrl,
            events: ["order.status.changed", "sell.order.status.changed"],
          };

          const payload = altPath + JSON.stringify(message);
          const signature = crypto
            .sign("sha256", Buffer.from(payload), privateKey)
            .toString("base64");

          const options = {
            method: "POST",
            url: altPath,
            headers: {
              "Content-Type": "application/json",
              "X-Api-Key": API_PUBLIC_KEY,
              "X-Api-Signature": signature,
            },
            data: message,
          };

          const response = await axios(options);

          return res.json({
            statusCode: 200,
            message:
              "Webhook registered successfully with alternative endpoint",
            data: response.data,
          });
        } catch (altError) {
          console.error("Error with alternative endpoint:", altError);
          return res.status(altError.response?.status || 500).json({
            statusCode: altError.response?.status || 500,
            message: "Failed to register webhook with both endpoints",
            details: altError.message,
          });
        }
      }

      return res.status(error.response?.status || 500).json({
        statusCode: error.response?.status || 500,
        message: error.message || "Failed to register webhook",
      });
    }
  },

  handleWebhook: async (req, res) => {
    try {
      console.log("Webhook received:", JSON.stringify(req.body));

      const signature = req.headers["x-api-signature"];
      const payload = JSON.stringify(req.body);

      const publicKey = crypto.createPublicKey({
        key: API_PUBLIC_KEY,
        format: "pem",
      });

      const isValid = crypto.verify(
        "sha256",
        Buffer.from(payload),
        publicKey,
        Buffer.from(signature, "base64")
      );

      if (!isValid) {
        console.error("Invalid webhook signature");
        return res.status(401).json({ error: "Invalid signature" });
      }

      const { event, data } = req.body;

      if (event === "order.status.changed") {
        const { externalOrderId, status } = data;
        const updatedOrder = await orderHistory.findOneAndUpdate(
          { externalOrderId },
          { status },
          { new: true }
        );

        console.log(`Order ${externalOrderId} status changed to ${status}`);

        // Handle different statuses
        if (status === "completed") {
          // Payment successful
          // TODO: Update your database, notify user, etc.
        } else if (status === "failed") {
          // Payment failed
          // TODO: Handle accordingly
        }
      } else if (event === "sell.order.status.changed") {
        // Handle sell order status changes similarly
        const { externalOrderId, status } = data;
        console.log(
          `Sell order ${externalOrderId} status changed to ${status}`
        );

        // TODO: Update your database with the new status
      }

      // Always respond with 200 to acknowledge receipt
      return res.status(200).json({ received: true });
    } catch (error) {
      console.error("Error handling webhook:", error);
      // Still return 200 to prevent Changelly from retrying
      return res.status(200).json({ received: true, error: error.message });
    }
  },
  getOrderStatus: async (req, res) => {
    try {
      const { externalOrderId } = req.params;

      if (!externalOrderId) {
        return res.status(400).json({
          statusCode: 400,
          message: "External order ID is required",
        });
      }

      console.log(`Checking status for order: ${externalOrderId}`);

      // First check if the order exists in our database
      const orderInDb = await orderHistory.findOne({ externalOrderId });

      if (!orderInDb) {
        return res.status(404).json({
          statusCode: 404,
          message: "Order not found in database",
        });
      }

      // If the order is very new and payment hasn't been initiated yet
      if (
        orderInDb.status === "pending" &&
        orderInDb.createdAt > new Date(Date.now() - 30 * 60 * 1000)
      ) {
        // 30 minutes
        return res.json({
          statusCode: 200,
          message: "Order created but payment not yet initiated",
          data: {
            externalOrderId: orderInDb.externalOrderId,
            status: orderInDb.status,
            paymentUrl: orderInDb.paymentUrl,
            orderType: orderInDb.orderType,
            currencyFrom: orderInDb.currencyFrom,
            currencyTo: orderInDb.currencyTo,
            amountFrom: orderInDb.amountFrom,
            providerCode: orderInDb.providerCode,
            createdAt: orderInDb.createdAt,
            message:
              "Payment link has been created but not yet used. Please direct the user to the payment URL to initiate payment.",
          },
        });
      }

      const privateKey = crypto.createPrivateKey({
        key: API_PRIVATE_KEY,
        type: "pkcs1",
        format: "pem",
        encoding: "base64",
      });

      // Try both buy and sell order endpoints
      const endpoints = [
        `https://fiat-api.changelly.com/v1/orders/${externalOrderId}`,
        `https://fiat-api.changelly.com/v1/sell/orders/${externalOrderId}`,
      ];

      let orderData = null;
      let errorDetails = null;

      // Try each endpoint
      for (const path of endpoints) {
        try {
          console.log(`Trying endpoint: ${path}`);
          const payload = path + JSON.stringify({});
          const signature = crypto
            .sign("sha256", Buffer.from(payload), privateKey)
            .toString("base64");

          const options = {
            method: "GET",
            url: path,
            headers: {
              "Content-Type": "application/json",
              "X-Api-Key": API_PUBLIC_KEY,
              "X-Api-Signature": signature,
            },
          };

          const response = await axios(options);

          if (response.data) {
            orderData = response.data;
            console.log("Order found:", orderData);
            break; // Exit the loop if we found the order
          }
        } catch (endpointError) {
          console.error(`Error with endpoint ${path}:`, endpointError.message);
          errorDetails = endpointError.response?.data || endpointError.message;
          // Continue to the next endpoint
        }
      }

      // If we found the order data
      if (orderData) {
        // Update order in database if needed
        try {
          await orderHistory.findOneAndUpdate(
            { externalOrderId },
            { status: orderData.status },
            { new: true, upsert: false }
          );
        } catch (dbError) {
          console.error("Database update error:", dbError);
          // Continue even if DB update fails
        }

        return res.json({
          statusCode: 200,
          message: "Order status retrieved",
          data: orderData,
        });
      }

      // If we couldn't find the order with either endpoint but it exists in our DB
      return res.json({
        statusCode: 200,
        message: "Order exists in database but not yet active in Changelly",
        data: {
          externalOrderId: orderInDb.externalOrderId,
          status: orderInDb.status,
          paymentUrl: orderInDb.paymentUrl,
          orderType: orderInDb.orderType,
          currencyFrom: orderInDb.currencyFrom,
          currencyTo: orderInDb.currencyTo,
          amountFrom: orderInDb.amountFrom,
          providerCode: orderInDb.providerCode,
          createdAt: orderInDb.createdAt,
          message:
            "The order exists in our system but hasn't been activated in Changelly yet. Please direct the user to the payment URL to initiate payment.",
        },
      });
    } catch (error) {
      console.error("Error getting order status:", error);
      return res.status(error.response?.status || 500).json({
        statusCode: error.response?.status || 500,
        message: error.message || "Failed to get order status",
        details: error.response?.data || null,
      });
    }
  },

  // Add a function to check by transaction ID
  checkTransactionStatus: async (req, res) => {
    try {
      const { transactionId } = req.params;

      if (!transactionId) {
        return res.status(400).json({
          statusCode: 400,
          message: "Transaction ID is required",
        });
      }

      const privateKey = crypto.createPrivateKey({
        key: API_PRIVATE_KEY,
        type: "pkcs1",
        format: "pem",
        encoding: "base64",
      });

      // Try to find the transaction in the transactions endpoint
      const path = `https://fiat-api.changelly.com/v1/transactions/${transactionId}`;
      const payload = path + JSON.stringify({});
      const signature = crypto
        .sign("sha256", Buffer.from(payload), privateKey)
        .toString("base64");

      const options = {
        method: "GET",
        url: path,
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": API_PUBLIC_KEY,
          "X-Api-Signature": signature,
        },
      };

      try {
        const response = await axios(options);

        return res.json({
          statusCode: 200,
          message: "Transaction status retrieved",
          data: response.data,
        });
      } catch (txError) {
        console.error("Error getting transaction:", txError.message);

        // If transaction not found, try to search in orders
        try {
          // Try to find the order by querying all recent orders
          const ordersPath = `https://fiat-api.changelly.com/v1/orders`;
          const ordersPayload = ordersPath + JSON.stringify({});
          const ordersSignature = crypto
            .sign("sha256", Buffer.from(ordersPayload), privateKey)
            .toString("base64");

          const ordersOptions = {
            method: "GET",
            url: ordersPath,
            headers: {
              "Content-Type": "application/json",
              "X-Api-Key": API_PUBLIC_KEY,
              "X-Api-Signature": ordersSignature,
            },
          };

          const ordersResponse = await axios(ordersOptions);

          // Find the order that contains this transaction ID
          const matchingOrder = ordersResponse.data.find(
            (order) =>
              order.transactionId === transactionId ||
              order.id === transactionId
          );

          if (matchingOrder) {
            return res.json({
              statusCode: 200,
              message: "Order found by transaction ID",
              data: matchingOrder,
            });
          }

          return res.status(404).json({
            statusCode: 404,
            message: "Transaction not found in any orders",
            details: txError.response?.data || null,
          });
        } catch (ordersError) {
          console.error("Error searching orders:", ordersError.message);
          return res.status(404).json({
            statusCode: 404,
            message: "Transaction not found",
            details: txError.response?.data || null,
            ordersError: ordersError.message,
          });
        }
      }
    } catch (error) {
      console.error("Error checking transaction:", error);
      return res.status(error.response?.status || 500).json({
        statusCode: error.response?.status || 500,
        message: error.message || "Failed to check transaction",
        details: error.response?.data || null,
      });
    }
  },

  getSellOrderStatus: async (req, res) => {
    try {
      const { externalOrderId } = req.params;

      if (!externalOrderId) {
        return res.status(400).json({
          statusCode: 400,
          message: "External order ID is required",
        });
      }

      const privateKey = crypto.createPrivateKey({
        key: API_PRIVATE_KEY,
        type: "pkcs1",
        format: "pem",
        encoding: "base64",
      });

      const path = `https://fiat-api.changelly.com/v1/sell/orders/${externalOrderId}`;
      const payload = path + JSON.stringify({});
      const signature = crypto
        .sign("sha256", Buffer.from(payload), privateKey)
        .toString("base64");

      const options = {
        method: "GET",
        url: path,
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": API_PUBLIC_KEY,
          "X-Api-Signature": signature,
        },
      };

      const response = await axios(options);

      // Update order in database
      if (response.data && response.data.status) {
        await orderHistory.findOneAndUpdate(
          { externalOrderId },
          { status: response.data.status },
          { new: true }
        );
      }

      return res.json({
        statusCode: 200,
        message: "Sell order status retrieved",
        data: response.data,
      });
    } catch (error) {
      console.error("Error getting sell order status:", error);
      return res.status(error.response?.status || 500).json({
        statusCode: error.response?.status || 500,
        message: error.message || "Failed to get sell order status",
      });
    }
  },
};

const getCurrencyDetails = async (currencyFrom) => {
  const privateKey = crypto.createPrivateKey({
    key: API_PRIVATE_KEY,
    type: "pkcs1",
    format: "pem",
    encoding: "base64",
  });

  let path = `https://fiat-api.changelly.com/v1/currencies`;
  const payload = path + JSON.stringify({});
  const signature = crypto
    .sign("sha256", Buffer.from(payload), privateKey)
    .toString("base64");

  const options = {
    method: "GET",
    url: path,
    headers: {
      "Content-Type": "application/json",
      "X-Api-Key": API_PUBLIC_KEY,
      "X-Api-Signature": signature,
    },
  };

  const response = await axios(options);
  const currencies = response.data;

  return currencies.find(
    (item) => item.ticker?.toLowerCase() === currencyFrom.toLowerCase()
  );
};
