const express = require("express");
const router = express.Router();
const userController = require("../controller/userController");

router.post("/register", userController.registerUser);
router.post("/dailyCheckIn", userController.dailyCheckIn);
router.get("/referral-stats", userController.getReferralStats);
router.get("/getAllRegisteredUsers", userController.getAllRegisteredUsers);
router.get("/status", userController.getUserStatus);
router.post("/redeem", userController.redeemPoints);

// Admin routes for redemptions
router.get("/admin/redemptions", userController.getAllRedemptions);
router.get("/admin/redemptions/by-wallet", userController.getRedemptionsByWalletAddress);



module.exports = router;
