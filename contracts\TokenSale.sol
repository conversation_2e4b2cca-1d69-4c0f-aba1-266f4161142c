// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@chainlink/contracts/src/v0.8/interfaces/AggregatorV3Interface.sol";

contract TokenSale is ReentrancyGuard, Ownable {
    using SafeERC20 for IERC20;

    struct Round {
        uint256 minBuyAmount;
        uint256 maxBuyAmount;
        uint256 tokenPrice;
        bool active;
        uint256 totalTokens;
        uint256 initialUnlock;
        uint256 cliffDuration;
        uint256 vestingDuration;
        uint256 tokensSold;
    }

    struct UserData {
        address user;
        uint256 roundId;
        uint256 purchaseTime;
        uint256 amount;
        uint256 bnbAmount;
        uint256 tokenAmount;
        uint256 tokenPrice;
        uint256 claimedAmount;
        uint256 nextClaimTime;
        uint256 lastClaimTime;
        bool isFullyClaimed;
        uint256 totalClaimed;
    }

    struct Purchase {
        uint256 amount;
    }

    Round[] public rounds;
    mapping(address => UserData[]) public userData;
    mapping(address => mapping(uint256 => Purchase)) public userPurchases;
    mapping(address => AggregatorV3Interface) public priceFeeds;

    address public immutable BnbAddress;
    address public fundsWallet;
    uint256 public maxCap;
    uint256 public totalTokensSold;

    event TokenPurchased(uint256 timestamp, address buyer, uint256 amount, uint256 cost);
    event TokensClaimed(address indexed user, uint256 amount);

    constructor(address _bnbAddress, address _fundsWallet, uint256 _maxCap) {
        require(_bnbAddress != address(0), "Invalid BNB address");
        require(_fundsWallet != address(0), "Invalid funds wallet");
        require(_maxCap > 0, "Invalid max cap");

        BnbAddress = _bnbAddress;
        fundsWallet = _fundsWallet;
        maxCap = _maxCap;
    }

    function startRound(
        uint256 _minBuyAmount,
        uint256 _allocationPercentage,
        uint256 _tokenPrice,
        uint256 _initialUnlock,
        uint256 _cliffDuration,
        uint256 _vestingDuration,
        bool _active
    ) external onlyOwner {
        uint256 remainingTokens = 0;
        if (rounds.length > 0) {
            Round storage lastRound = rounds[rounds.length - 1];
            lastRound.active = false;
            remainingTokens = lastRound.maxBuyAmount - lastRound.tokensSold;
        }

        uint256 totalTokens = (_allocationPercentage * maxCap) / 100;
        uint256 initialUnlock = _initialUnlock / 100;

        rounds.push(
            Round({
                minBuyAmount: _minBuyAmount,
                maxBuyAmount: totalTokens + remainingTokens,
                tokenPrice: _tokenPrice,
                active: _active,
                totalTokens: totalTokens,
                initialUnlock: initialUnlock,
                cliffDuration: _cliffDuration,
                vestingDuration: _vestingDuration,
                tokensSold: 0
            })
        );
    }

    function buyWithToken(
        address _tokenAddress,
        uint256 _amount
    ) external nonReentrant {
        Round storage currentRound = rounds[rounds.length - 1];
        UserData[] storage user = userData[msg.sender];
        require(currentRound.active, "No active round");

        uint256 tokenCost = (_amount * currentRound.tokenPrice) / 1e18;

        require(
            currentRound.tokensSold + _amount <= currentRound.maxBuyAmount,
            "Exceeds max cap for this round"
        );
        require(totalTokensSold + _amount <= maxCap, "Max cap reached");

        uint256 allowance = IERC20(_tokenAddress).allowance(
            msg.sender,
            address(this)
        );
        require(allowance >= tokenCost, "Token allowance too low");

        uint256 balance = IERC20(_tokenAddress).balanceOf(msg.sender);
        require(balance >= tokenCost, "Insufficient token balance");

        IERC20(_tokenAddress).safeTransferFrom(
            msg.sender,
            fundsWallet,
            tokenCost
        );

        currentRound.tokensSold += _amount;
        totalTokensSold += _amount;

        Purchase storage purchase = userPurchases[msg.sender][
            rounds.length - 1
        ];
        purchase.amount += _amount;

        user.push(
            UserData(
                msg.sender,
                rounds.length - 1,
                block.timestamp,
                _amount,
                0,
                tokenCost,
                currentRound.tokenPrice,
                0,
                0,
                0,
                false,
                0
            )
        );

        claimDataByIndex(userData[msg.sender].length - 1);
        claimingData(msg.sender);
        emit TokenPurchased(block.timestamp, msg.sender, _amount, tokenCost);
    }

    function buyWithBNB() external payable nonReentrant {
        require(msg.value > 0, "No BNB sent");

        Round storage currentRound = rounds[rounds.length - 1];
        UserData[] storage user = userData[msg.sender];

        require(currentRound.active, "No active round");

        uint256 bnbPrice = getLatestPrice(
            address(priceFeeds[address(BnbAddress)])
        );

        uint256 tokenAmount = (msg.value * bnbPrice) / currentRound.tokenPrice;

        require(
            currentRound.tokensSold + tokenAmount <= currentRound.maxBuyAmount,
            "Exceeds max cap for this round"
        );
        require(totalTokensSold + tokenAmount <= maxCap, "Max cap reached");

        currentRound.tokensSold += tokenAmount;
        totalTokensSold += tokenAmount;

        Purchase storage purchase = userPurchases[msg.sender][
            rounds.length - 1
        ];
        purchase.amount += tokenAmount;

        user.push(
            UserData(
                msg.sender,
                rounds.length - 1,
                block.timestamp,
                tokenAmount,
                msg.value,
                0,
                currentRound.tokenPrice,
                0,
                0,
                0,
                false,
                0
            )
        );

        claimDataByIndex(userData[msg.sender].length - 1);
        claimingData(msg.sender);
        payable(fundsWallet).transfer(msg.value);

        emit TokenPurchased(
            block.timestamp,
            msg.sender,
            tokenAmount,
            msg.value
        );
    }

    function claimDataByIndex(uint256 index) internal {
        UserData storage data = userData[msg.sender][index];
        Round storage round = rounds[data.roundId];
        
        if (data.nextClaimTime == 0) {
            data.nextClaimTime = data.purchaseTime + round.cliffDuration;
        }

        uint256 initialAmount = (data.amount * round.initialUnlock);
        if (data.claimedAmount < initialAmount) {
            data.claimedAmount = initialAmount;
            data.totalClaimed += initialAmount;
        }
    }

    function claimingData(address _user) internal {
        UserData[] storage userDataArray = userData[_user];
        for (uint256 i = 0; i < userDataArray.length; i++) {
            UserData storage data = userDataArray[i];
            Round storage round = rounds[data.roundId];

            if (data.isFullyClaimed) continue;

            if (block.timestamp >= data.nextClaimTime) {
                uint256 timePassed = block.timestamp - data.purchaseTime;
                if (timePassed >= round.vestingDuration) {
                    uint256 remainingClaimable = data.amount - data.claimedAmount;
                    data.claimedAmount = data.amount;
                    data.totalClaimed += remainingClaimable;
                    data.isFullyClaimed = true;
                    data.lastClaimTime = block.timestamp;
                    emit TokensClaimed(_user, remainingClaimable);
                } else {
                    uint256 vestedAmount = (data.amount * timePassed) / round.vestingDuration;
                    uint256 newClaimable = vestedAmount - data.claimedAmount;
                    if (newClaimable > 0) {
                        data.claimedAmount = vestedAmount;
                        data.totalClaimed += newClaimable;
                        data.lastClaimTime = block.timestamp;
                        data.nextClaimTime = block.timestamp + 1 days;
                        emit TokensClaimed(_user, newClaimable);
                    }
                }
            }
        }
    }

    function getLatestPrice(address priceFeed) internal view returns (uint256) {
        require(priceFeed != address(0), "Invalid price feed address");
        AggregatorV3Interface feed = AggregatorV3Interface(priceFeed);
        (, int256 price, , , ) = feed.latestRoundData();
        require(price > 0, "Invalid price data");
        return uint256(price);
    }

    function setPriceFeed(address token, address feed) external onlyOwner {
        require(token != address(0), "Invalid token address");
        require(feed != address(0), "Invalid feed address");
        priceFeeds[token] = AggregatorV3Interface(feed);
    }

    function setFundsWallet(address _newWallet) external onlyOwner {
        require(_newWallet != address(0), "Invalid wallet address");
        fundsWallet = _newWallet;
    }
}