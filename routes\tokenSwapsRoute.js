const express = require("express");
const router = express.Router();
const TokenSwapController = require("../controller/tokenSwapsController");

router.post("/tokenSwap", TokenSwapController.createTokenSwapsData);
router.get(
  "/walletAmount/:walletAddress",
  TokenSwapController.getWalletAddressSwapsAmount
);

router.post("/buyTranscation", TokenSwapController.buyTransaction);
router.post("/sellTranscation", TokenSwapController.sellTransaction);
router.get("/getAllTokenAirDrop", TokenSwapController.getAllAirDropData);
router.get("/getAllSwapForAdmin", TokenSwapController.getAllSwapForAdmin);
router.get(
  "/getWalletAddressSwaps/:walletAddress",
  TokenSwapController.getWalletAddressSwaps
);

module.exports = router;
