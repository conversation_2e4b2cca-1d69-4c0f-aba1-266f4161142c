const asyncHandler = require("express-async-handler");
const TokenSwap = require("../model/tokenSwaps");
const { DateTime } = require("luxon");
const { BuyTransaction, SellTransaction } = require("../model/buyTranscation");
const DCA = require("../model/DCA");
const { LAMPORTS_PER_SOL } = require("@solana/web3.js");
const axios = require("axios");
const { paginate } = require("../utils");
// Cache for token metadata to avoid repeated API calls
const tokenMetadataCache = new Map();

// Dynamic function to get token metadata including decimals
const getTokenMetadata = async (mintAddress) => {
  // Check cache first
  if (tokenMetadataCache.has(mintAddress)) {
    return tokenMetadataCache.get(mintAddress);
  }

  // Handle SOL token
  if (mintAddress === "So11111111111111111111111111111111111111112") {
    const metadata = {
      symbol: "<PERSON><PERSON>",
      name: "<PERSON><PERSON>",
      decimals: 9
    };
    tokenMetadataCache.set(mintAddress, metadata);
    return metadata;
  }

  try {
    // Fetch token metadata from Moralis API
    const headers = { "X-API-Key": process.env.MORALIS_API_KEY };
    const response = await axios.get(
      `https://solana-gateway.moralis.io/token/mainnet/${mintAddress}/metadata`,
      { headers }
    );

    const metadata = {
      symbol: response.data?.symbol || `${mintAddress.slice(0, 4)}...${mintAddress.slice(-4)}`,
      name: response.data?.name || response.data?.symbol || "Unknown Token",
      decimals: response.data?.decimals || 6 // Default to 6 decimals if not available
    };

    // Cache the result
    tokenMetadataCache.set(mintAddress, metadata);
    return metadata;
  } catch (error) {
    console.log(`Error fetching metadata for ${mintAddress}:`, error.message);
    
    // Fallback metadata
    const fallbackMetadata = {
      symbol: `${mintAddress.slice(0, 4)}...${mintAddress.slice(-4)}`,
      name: "Unknown Token",
      decimals: 6 // Default decimals
    };
    
    tokenMetadataCache.set(mintAddress, fallbackMetadata);
    return fallbackMetadata;
  }
};

const createTokenSwapsData = asyncHandler(async (req, res) => {
  const {
    walletAddress,
    inputToken,
    outputToken,
    inputAmount,
    transactionHash,
  } = req.body;

  if (
    !walletAddress ||
    !inputToken ||
    !outputToken ||
    !inputAmount ||
    !transactionHash
  ) {
    res.status(400);
    throw new Error("Please fill all the required fields");
  }
  const tokenSwapData = await TokenSwap.create({
    walletAddress,
    inputToken,
    outputToken,
    inputAmount,
    transactionHash,
  });

  await tokenSwapData.save();

  return res.status(200).json({
    message: "Token Swap Data created successfully",
    data: tokenSwapData,
  });
});

const getWalletAddressSwapsAmount = asyncHandler(async (req, res) => {
  const walletAddress = req.params.walletAddress;
  if (!walletAddress) {
    res.status(400);
    throw new Error("Wallet address is required");
  }
  const result = await TokenSwap.aggregate([
    { $match: { walletAddress: walletAddress } },
    {
      $group: {
        _id: "$walletAddress",
        totalInputAmount: { $sum: "$inputAmount" },
      },
    },
  ]);

  const total = result.length > 0 ? result[0].totalInputAmount : 0;

  return res.status(200).json({
    walletAddress,
    totalInputAmount: total,
  });
});

const buyTransaction = asyncHandler(async (req, res) => {
  const {
    walletAddress,
    amount,
    name,
    currencyBought,
    fiatCurrency,
    amountReceived,
  } = req.body;

  const tokenSwapData = await BuyTransaction.create({
    walletAddress,
    amount,
    name,
    currencyBought,
    fiatCurrency,
    amountReceived,
  });

  return res.status(200).json({
    message: "Buy transaction created successfully",
    data: tokenSwapData,
  });
});

const sellTransaction = asyncHandler(async (req, res) => {
  const {
    walletAddress,
    sellAmount,
    name,
    amount,
    currencySold,
    fiatCurrency,
    amountSold,
  } = req.body;

  const tokenSwapData = await SellTransaction.create({
    walletAddress,
    sellAmount,
    name,
    amount,
    currencySold,
    fiatCurrency,
    amountSold,
  });

  return res.status(200).json({
    message: "Sell transaction created successfully",
    data: tokenSwapData,
  });
});

const getAllAirDropData = asyncHandler(async (req, res) => {
  const { tier, minDca, maxDca, minSwap, maxSwap, fromDate, toDate } =
    req.query;

  const tokenSwapData = await TokenSwap.find().select();

  if (!tokenSwapData || tokenSwapData.length === 0) {
    res.status(404);
    throw new Error("No TokenSwap data found");
  }

  const populatedTokenSwapData = await Promise.all(
    tokenSwapData.map(async (tokenSwap) => {
      const dcaData = await DCA.findOne({
        walletAddress: tokenSwap.walletAddress,
      }).select("amount");

      let category = "Gaya Bronze";
      let level = 3;

      if (tokenSwap.inputAmount >= 500) {
        category = "Gaya Gold";
        level = 1;
      } else if (tokenSwap.inputAmount >= 100) {
        category = "Gaya Silver";
        level = 2;
      }

      return {
        ...tokenSwap.toObject(),
        DCAAmount: dcaData ? dcaData.amount : null,
        category,
        level,
        createdAt: tokenSwap.createdAt,
      };
    })
  );

  let filteredData = populatedTokenSwapData;

  // Apply tier filter (level)
  if (tier && tier !== "All") {
    filteredData = filteredData.filter((data) => data.level === Number(tier));
  }

  // Apply DCA amount range filter
  if (minDca || maxDca) {
    filteredData = filteredData.filter((data) => {
      const amount = Number(data.DCAAmount || 0);
      return (
        (!minDca || amount >= Number(minDca)) &&
        (!maxDca || amount <= Number(maxDca))
      );
    });
  }

  // Apply Swap amount range filter
  if (minSwap || maxSwap) {
    filteredData = filteredData.filter((data) => {
      const amount = Number(data.inputAmount);
      return (
        (!minSwap || amount >= Number(minSwap)) &&
        (!maxSwap || amount <= Number(maxSwap))
      );
    });
  }

  // Apply date filter
  if (fromDate || toDate) {
    const from = fromDate
      ? DateTime.fromISO(fromDate).startOf("day").toJSDate()
      : null;
    const to = toDate ? DateTime.fromISO(toDate).endOf("day").toJSDate() : null;

    filteredData = filteredData.filter((data) => {
      const createdAt = new Date(data.createdAt);
      return (!from || createdAt >= from) && (!to || createdAt <= to);
    });
  }

  return res.status(200).json({
    message: "Token data fetched successfully",
    data: filteredData,
  });
});

const getAllSwapForAdmin = asyncHandler(async (req, res) => {
  const {page} = req.query;
  const tokenSwapData = await TokenSwap.find();
  
  if (!tokenSwapData || tokenSwapData.length === 0) {
    res.status(404);
    throw new Error("No TokenSwap data found");
  }

  // Transform each swap to replace inputToken and outputToken with token names
  const transformedData = await Promise.all(
    tokenSwapData.map(async (swap) => {
      let inputTokenMetadata = { symbol: swap.inputToken, name: swap.inputToken, decimals: 6 };
      let outputTokenMetadata = { symbol: swap.outputToken, name: swap.outputToken, decimals: 6 };
      
      // Get token metadata dynamically
      if (swap.inputToken && swap.inputToken !== null) {
        try {
          inputTokenMetadata = await getTokenMetadata(swap.inputToken);
        } catch (error) {
          console.log(`Error getting input token metadata for ${swap.inputToken}:`, error.message);
        }
      }
      
      if (swap.outputToken && swap.outputToken !== null) {
        try {
          outputTokenMetadata = await getTokenMetadata(swap.outputToken);
        } catch (error) {
          console.log(`Error getting output token metadata for ${swap.outputToken}:`, error.message);
        }
      }
      
      // Convert amount based on token decimals
      let convertedInputAmount = swap.inputAmount;
      if (inputTokenMetadata.decimals > 0) {
        convertedInputAmount = Number(swap.inputAmount) / Math.pow(10, inputTokenMetadata.decimals);
      }
      
      return {
        ...swap.toObject(),
        inputToken: inputTokenMetadata.symbol,
        inputTokenName: inputTokenMetadata.name,
        outputToken: outputTokenMetadata.symbol,
        outputTokenName: outputTokenMetadata.name,
        inputAmount: convertedInputAmount,
        originalInputAmount: swap.inputAmount, // Keep original for reference
        inputTokenDecimals: inputTokenMetadata.decimals,
        outputTokenDecimals: outputTokenMetadata.decimals,
      };
    })
  );
  const { paginatedItems, totalPages, currentPage, totalItems } = paginate(transformedData, 15, page);


  return res.status(200).json({
    message: "Token data fetched successfully",
    data: paginatedItems,
    totalPages,
    currentPage,
    totalItems,
  });
});

const getWalletAddressSwaps = asyncHandler(async (req, res) => {
  const walletAddress = req.params.walletAddress;
  if (!walletAddress) {
    res.status(400);
    throw new Error("Wallet address is required");
  }
  const tokenSwapData = await TokenSwap.find({
    walletAddress: walletAddress,
  });

  if (!tokenSwapData || tokenSwapData.length === 0) {
    res.status(404);
    throw new Error("No TokenSwap data found");
  }
  return res.status(200).json({
    message: "Token data fetched successfully",
    data: tokenSwapData,
  });
});

module.exports = {
  createTokenSwapsData,
  getWalletAddressSwapsAmount,
  buyTransaction,
  sellTransaction,
  getAllAirDropData,
  getAllSwapForAdmin,
  getWalletAddressSwaps,
};
