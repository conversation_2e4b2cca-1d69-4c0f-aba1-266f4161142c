const mongoose = require("mongoose");

const orderHistorySchema = new mongoose.Schema({
  externalOrderId: {
    type: String,
  },
  providerCode: {
    type: String,
  },
  orderType: {
    type: String,
    enum: ["buy", "sell"],
  },
  currencyFrom: {
    type: String,
  },
  currencyTo: {
    type: String,
  },
  amountFrom: {
    type: Number,
  },
  status: {
    type: String,
    enum: ["pending", "processing", "completed", "failed", "cancelled"],
    default: "pending",
  },
  paymentUrl: String,
  transactionHash: String,
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

module.exports = mongoose.model("OrderHistory", orderHistorySchema);
