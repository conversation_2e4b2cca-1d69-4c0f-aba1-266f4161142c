const express = require("express");
const router = express.Router();
const pointsController = require("../controller/pointsController");

router.get("/allPoints", pointsController.getAllPoints);
router.get("/history/:walletAddress", pointsController.getAllPointsHistory);
router.get(
  "/walletPoints/:walletAddress",
  pointsController.getWalletAddressPoints
);
router.post(
  "/award-dca-history-points",
  pointsController.awardPointsForDCAOrderHistory
);

module.exports = router;
