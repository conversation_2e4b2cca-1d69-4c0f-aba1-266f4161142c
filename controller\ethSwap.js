const Admin = require("../model/admin");
const jwt = require("jsonwebtoken");
const asyncHandler = require("express-async-handler");
const { parseUnits } = require("ethers/lib/utils");

const {
  <PERSON><PERSON><PERSON>er,
  SwapType,
  nativeOn<PERSON>hain,
  MAX_UINT160,
} = require("@uniswap/smart-order-router");
const { AllowanceTransfer, PermitSingle } = require("@uniswap/permit2-sdk");
const { ethers } = require("ethers");
const V3_SWAP_ROUTER_ADDRESS = "******************************************";
const {
  Token,
  CurrencyAmount,
  TradeType,
  Percent,
  computePriceImpact,
  WETH9,
} = require("@uniswap/sdk-core");
const { ChainId } = require("@uniswap/sdk");
const { default: axios } = require("axios");
const wallet = require("../model/wallet");
const TokenSwaps = require("../model/tokenSwaps");
// Configuration
// const INFURA_URL =
//   "https://frequent-summer-friday.quiknode.pro/2c0b05f084892dfb225d7a21eefafdb54dd233d6";

const INFURA_URL =
  "https://billowing-quaint-sanctuary.bsc.quiknode.pro/055ed6d4e8ca39dd51f263d3c1d2fb05f96ca6bf";
const provider = new ethers.providers.JsonRpcProvider(INFURA_URL);
const PERMIT2_ADDRESS = "******************************************";
const PERMIT2_APPROVE_ABI = [
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        internalType: "address",
        name: "spender",
        type: "address",
      },
      {
        internalType: "uint160",
        name: "amount",
        type: "uint160",
      },
      {
        internalType: "uint48",
        name: "expiration",
        type: "uint48",
      },
    ],
    name: "approve",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
];
const makePermit = (tokenAddress, amount, nonce, universalRouter) => {
  const deadline = Math.floor(Date.now() / 1000 + 1800); // 30 minutes from now
  return {
    details: {
      token: tokenAddress,
      amount: amount, // Use the actual amount instead of MAX_UINT160
      expiration: deadline,
      nonce,
    },
    spender: universalRouter,
    sigDeadline: deadline,
  };
};
// async function generatePermitSignature(permit, signer, chainId) {
//   const { domain, types, values } = AllowanceTransfer.getPermitData(
//     permit,
//     PERMIT2_ADDRESS,
//     chainId
//   );
//   return await signer._signTypedData(domain, types, values);
// }

async function generatePermitSignature(permit, chainId, permitAddress, signer) {
  const { domain, types, values } = AllowanceTransfer.getPermitData(
    permit,
    permitAddress,
    chainId
  );
  return await signer._signTypedData(domain, types, values);
  // return { domain, types, values };
}

function toInputPermit(signature, permit) {
  return {
    ...permit,
    signature,
  };
}

exports.ethSwap = asyncHandler(async (req, res) => {
  const {
    transactionType,
    inputTokenAddress,
    outputTokenAddress,
    inputAmount,
    inputDecimals,
    outPutDecimals,
    InputtokenName,
    outPuttokenName,
    Inputsymbol,
    outputsymbol,
    address,
    chainId,
    slipage = 50, // Default 0.5% slippage
    isNative,
    isNativeOut,
  } = req.body;
  console.log("this is the request body", req.body);

  // Validate required parameters
  if (
    !inputTokenAddress ||
    !outputTokenAddress ||
    !inputAmount ||
    !address ||
    !chainId
  ) {
    return res.status(400).json({
      error: "Missing required parameters",
      details:
        "inputTokenAddress, outputTokenAddress, inputAmount, address, and chainId are required",
    });
  }

  try {
    // Initialize AlphaRouter
    const router = new AlphaRouter({
      chainId: Number(chainId),
      provider: getProviderForChain(chainId), // Implement this helper function
    });

    // Create token instances
    const inputToken = isNative
      ? nativeOnChain(chainId)
      : new Token(
          chainId,
          inputTokenAddress,
          parseInt(inputDecimals),
          Inputsymbol,
          InputtokenName
        );

    const outputToken = new Token(
      chainId,
      outputTokenAddress,
      parseInt(outPutDecimals),
      outputsymbol,
      outPuttokenName
    );

    // Prepare amount and swap parameters
    const parsedInputAmount = parseUnits(
      inputAmount.toString(),
      inputToken.decimals
    );
    const amountIn = CurrencyAmount.fromRawAmount(
      inputToken,
      parsedInputAmount
    );
    const tradeType =
      transactionType === "BUY"
        ? TradeType.EXACT_OUTPUT
        : TradeType.EXACT_INPUT;

    // Configure swap options
    const SLIPPAGE_TOLERANCE = new Percent(slipage, 10_000); // 0.5% slippage
    const outputCurrency = isNativeOut ? nativeOnChain(chainId) : outputToken;

    console.log("this is spliage", SLIPPAGE_TOLERANCE);
    console.log("this is the input token", inputToken);
    console.log("this is the output token", outputToken);
    console.log("this is the input amount", amountIn);
    console.log("this is the trade type", tradeType);
    console.log("this is the output currency", outputCurrency);

    const route = await router.route(
      amountIn,
      outputCurrency,
      TradeType.EXACT_INPUT,
      {
        recipient: address,
        slippageTolerance: SLIPPAGE_TOLERANCE,
        deadline: Math.floor(Date.now() / 1000 + 1800),
        type: SwapType.UNIVERSAL_ROUTER,
      },
      {
        maxSwapsPerPath: 2,
        protocols: ['V3'],
        forceCrossProtocol: false
      }
    );

    console.log(route);

    if (!route || !route.methodParameters) {
      return res
        .status(400)
        .json({ error: "No valid route found for the swap" });
    }

    // Calculate price impact
    const priceImpact = calculatePriceImpact(route);
    const gasEstimate = {
      usd: route.estimatedGasUsedUSD.toFixed(2),
      wei: route.gasPriceWei.toString(),
      units: route.estimatedGasUsed.toString(),
    };

    // Prepare response
    return res.json({
      success: true,
      transaction: {
        input: {
          amount: amountIn.toExact(),
          token: inputToken.symbol,
        },
        output: {
          amount: route.quote.toExact(),
          token: outputToken.symbol,
        },
        priceImpact: `${priceImpact.toFixed(3)}%`,
        gasEstimate,
        methodParameters: route.methodParameters,
      },
    });
  } catch (error) {
    console.error("Swap error:", error);
    return res.status(500).json({
      error: "Swap failed",
      details: error.message,
      ...(process.env.NODE_ENV === "development" && { stack: error.stack }),
    });
  }
});

// Helper function to calculate price impact
function calculatePriceImpact(route) {
  try {
    const midPrice = route.trade.swaps[0]?.route.midPrice;
    if (!midPrice) return 0.001; // Default minimal impact

    const inputAmount = CurrencyAmount.fromRawAmount(
      midPrice.baseCurrency,
      route.trade.inputAmount.quotient.toString()
    );

    const outputAmount = CurrencyAmount.fromRawAmount(
      midPrice.quoteCurrency,
      route.trade.outputAmount.quotient.toString()
    );

    const impact = computePriceImpact(midPrice, inputAmount, outputAmount);
    return parseFloat(impact.toSignificant(4));
  } catch {
    return 0.001; // Fallback value
  }
}

// Helper function to get provider for chain
function getProviderForChain(chainId) {
  const rpcUrls = {
    1: "https://frequent-summer-friday.quiknode.pro/2c0b05f084892dfb225d7a21eefafdb54dd233d6",
    56: "https://billowing-quaint-sanctuary.bsc.quiknode.pro/055ed6d4e8ca39dd51f263d3c1d2fb05f96ca6bf",
  };

  return new ethers.providers.JsonRpcProvider(rpcUrls[chainId]);
}

exports.tokenList = asyncHandler(async (req, res) => {
  try {
    // Fetch the Uniswap token list from IPFS
    const response = await axios.get("https://tokens.uniswap.org");
    console.log(response);
    res.status(200).json({
      success: true,
      data: response.data.tokens, // or just response.data if you want the full list metadata
    });
  } catch (error) {
    console.error("Error fetching token list:", error.message);

    // Send a structured error response
    res.status(500).json({
      success: false,
      message: "Failed to fetch token list",
      error: error.message, // Avoid exposing sensitive details in production
    });
  }
});

function swapOptions(options, recipientAddress, slippageTolerance) {
  const slippage =
    slippageTolerance !== undefined
      ? new Percent(slippageTolerance, 10_000) // Changed from 1000 to 10_000
      : undefined;

  return Object.assign(
    {
      recipient: recipientAddress,
      slippageTolerance: slippage,
      deadline: Math.floor(Date.now() / 1000 + 1800),
      type: SwapType.UNIVERSAL_ROUTER,
    },
    options
  );
}
exports.executeSwap = asyncHandler(async (req, res) => {
  const {
    transactionType,
    inputTokenAddress,
    outputTokenAddress,
    inputAmount,
    inputDecimals,
    outPutDecimals,
    inputTokenName,
    outputTokenName,
    inputSymbol,
    outPutSymbol,
    address,
    privateKey, // Private key sent in the request body
    isNative,
    isNativeOut,
    chainId,
  } = req.body;
  try {
    if (!address) {
      return res.status(400).json({ error: "Recipient address is required" });
    }
    if (!privateKey || !ethers.utils.isHexString(privateKey)) {
      return res.status(400).json({ error: "Invalid private key" });
    }

    const wallet = new ethers.Wallet(privateKey, provider);
    const walletBalance = await wallet.getBalance();
    if (walletBalance.isZero()) {
      return res.status(203).json({
        error: "Insufficient funds for gas",
        details:
          "Wallet has 0 ETH. Please fund the wallet with ETH to cover gas fees.",
      });
    }
    const inputToken = new Token(
      chainId,
      inputTokenAddress,
      parseInt(inputDecimals),
      inputSymbol,
      inputTokenName
    );

    const outputToken = new Token(
      chainId,
      outputTokenAddress,
      parseInt(outPutDecimals),
      outPutSymbol,
      outputTokenName
    );
    let inAmount;
    let opts;

    if (isNative) {
      opts = swapOptions({}, address, 50);
      inAmount = CurrencyAmount.fromRawAmount(nativeOnChain(56), inputAmount);
    }
    const router = new AlphaRouter({ chainId: chainId, provider });
    console.log(opts);

    const route = await router.route(
      inAmount,
      outputToken,
      TradeType.EXACT_INPUT,
      opts,
      {
        maxSwapsPerPath: 4,
      }
    );
    const sendTxn = {
      data: route.methodParameters.calldata,
      to: route.methodParameters.to,
      value: route.methodParameters.value,
    };
    const estimatedGas = await provider.estimateGas({
      ...sendTxn,
      from: address,
    });
    const estGas = estimatedGas.toString();

    console.log("Estimated Gas:", estGas);

    const sendFinalTxn = {
      ...sendTxn,
      from: address,
      //   gasLimit: estGas,
      //   gasPrice: route.gasPriceWei,
    };
    const transcationHash = await wallet.sendTransaction(sendFinalTxn);
    await transcationHash.wait();
    res.json({
      message: "Swap transaction executed successfully!",
      transactionHash: transcationHash.hash,
    });
  } catch (error) {
    console.log(error);
  }
});

exports.getSignData = async (req, res, next) => {
  try {
    const {
      tokenInAddress,
      tokenInDecimal,
      amountIn,
      recipientAddress,
      chainID,
      privateKey,
    } = req.body;

    if (
      !tokenInAddress ||
      !tokenInDecimal ||
      !amountIn ||
      !recipientAddress ||
      !chainID
    ) {
      return res.status(400).json({
        error: "Missing required parameters",
        details:
          "tokenInAddress, tokenInDecimal, amountIn, recipientAddress, chainID are required",
      });
    }

    const result = await getSignedData({
      tokenInAddress,
      tokenInDecimal,
      amountIn,
      recipientAddress,
      chainID,
      privateKey,
    });

    if (result.error) {
      return res.status(400).json({
        error: result.error,
        details: result.details,
      });
    }
    return res.status(200).json({
      message: "Sign Data Created!",
      data: result.data,
    });
  } catch (ex) {
    next(ex);
  }
};

const getSignedData = async ({
  tokenInAddress,
  tokenInDecimal,
  amountIn,
  recipientAddress,
  chainID,
  privateKey,
}) => {
  try {
    console.log(
      tokenInAddress,
      tokenInDecimal,
      amountIn,
      recipientAddress,
      chainID
    );
    const rpcs = {
      1: "https://frequent-summer-friday.quiknode.pro/2c0b05f084892dfb225d7a21eefafdb54dd233d6",
      56: "https://billowing-quaint-sanctuary.bsc.quiknode.pro/055ed6d4e8ca39dd51f263d3c1d2fb05f96ca6bf",
      // Add other chain IDs and their RPC URLs as needed
    };

    const permits = {
      1: "******************************************", // Mainnet Permit2
      56: "******************************************", // BSC Permit2
      // Add other chain IDs and their Permit2 addresses
    };

    const universalRouters = {
      1: "******************************************", // Mainnet Universal Router
      56: "******************************************", // BSC Universal Router
      // Add other chain IDs and their Universal Router addresses
    };

    const UNISWAP_PERMIT2_ABI = [
      "function allowance(address owner, address token, address spender) external view returns (uint160 amount, uint48 expiration, uint48 nonce)",
    ];

    const ethProvider = new ethers.providers.JsonRpcProvider(rpcs[chainID]);
    const wallet = new ethers.Wallet(privateKey, ethProvider);

    const UNISWAP_PERMIT_CONTRACT = new ethers.Contract(
      permits[chainID],
      UNISWAP_PERMIT2_ABI,
      ethProvider
    );

    const { nonce } = await UNISWAP_PERMIT_CONTRACT.allowance(
      recipientAddress,
      tokenInAddress,
      universalRouters[chainID]
    );
    console.log("this is the nonce", nonce);

    const stringValue = amountIn.toString();
    const amountInEther = ethers.utils
      .parseUnits(stringValue, tokenInDecimal)
      .toString();
    console.log("before permit", amountInEther);
    const permit = makePermit(
      tokenInAddress,
      amountInEther,
      nonce,
      universalRouters[chainID]
    );
    console.log("after permit", permit, chainID, permits[chainID], wallet);

    const signature = await generatePermitSignature(
      permit,
      chainID,
      permits[chainID],
      wallet
    );

    return {
      data: {
        permit,
        signature,
      },
    };
  } catch (error) {
    console.error("Error in getSignedData:", error);
    return {
      error: error.message,
    };
  }
};

// Updated version of the swapTokenUniversal with working Permit2 or approval support

exports.swapTokenUniversal = async (req, res, next) => {
  try {
    const {
      tokenInAddress,
      tokenInDecimal,
      tokenInSymbol,
      tokenInName,
      tokenOutAddress,
      tokenOutDecimal,
      tokenOutSymbol,
      tokenOutName,
      amountIn,
      slippageTolerance = 50,
      recipientAddress,
      isNative,
      signature,
      permit,
      chainID,
      isNativeOut,
      privateKey,
    } = req.body;

    console.log("this is token data", req.body);

    if (
      !tokenInAddress ||
      !tokenOutAddress ||
      !amountIn ||
      !recipientAddress ||
      !chainID
    ) {
      return res.status(400).json({
        error: "Missing required parameters",
        details:
          "tokenInAddress, tokenOutAddress, amountIn, recipientAddress, chainID are required",
      });
    }

    const result = await swapTokenUniversal({
      tokenInAddress,
      tokenInDecimal,
      tokenInSymbol,
      tokenInName,
      tokenOutAddress,
      tokenOutDecimal,
      tokenOutSymbol,
      tokenOutName,
      amountIn,
      slippageTolerance,
      recipientAddress,
      isNative,
      signature,
      permit,
      chainID,
      isNativeOut,
      privateKey,
    });

    if (result.error) {
      return res.status(400).json({
        error: result.error,
        details: result.details,
        stack: result.stack,
      });
    }

    return res.status(200).json({
      message: "Tokens Swapped!",
      data: result,
    });
  } catch (ex) {
    console.error("Swap error:", ex);
    return res.status(500).json({
      error: "Internal Server Error",
      details: ex.message,
    });
  }
};

// const swapTokenUniversal = async (swapParams) => {
//   try {
//     const {
//       tokenInAddress,
//       tokenInDecimal,
//       tokenInSymbol,
//       tokenInName,
//       tokenOutAddress,
//       tokenOutDecimal,
//       tokenOutSymbol,
//       tokenOutName,
//       amountIn,
//       slippageTolerance,
//       recipientAddress,
//       isNative,
//       signature,
//       permit,
//       chainID,
//       isNativeOut,
//       privateKey,
//     } = swapParams;

//     const supportedChains = [1, 56];
//     if (!supportedChains.includes(Number(chainID))) {
//       throw new Error(`Unsupported chain ID: ${chainID}`);
//     }

//     const chainConfig = {
//       1: {
//         rpc: "https://frequent-summer-friday.quiknode.pro/2c0b05f084892dfb225d7a21eefafdb54dd233d6",
//         permit2: "******************************************",
//         universalRouter: "******************************************",
//       },
//       56: {
//         rpc: "https://billowing-quaint-sanctuary.bsc.quiknode.pro/055ed6d4e8ca39dd51f263d3c1d2fb05f96ca6bf",
//         permit2: "******************************************",
//         universalRouter: "******************************************",
//       },
//     };

//     const provider = new ethers.providers.JsonRpcProvider(
//       chainConfig[chainID].rpc
//     );
//     const wallet = new ethers.Wallet(privateKey, provider);

//     const tokenIn = isNative
//       ? nativeOnChain(chainID)
//       : new Token(
//           chainID,
//           tokenInAddress,
//           parseInt(tokenInDecimal),
//           tokenInSymbol,
//           tokenInName
//         );

//     const tokenOut = isNativeOut
//       ? WETH9[chainID]
//       : new Token(
//           chainID,
//           tokenOutAddress,
//           parseInt(tokenOutDecimal),
//           tokenOutSymbol,
//           tokenOutName
//         );

//     const parsedAmount = ethers.utils.parseUnits(
//       amountIn.toString(),
//       tokenInDecimal
//     );
//     const inAmount = CurrencyAmount.fromRawAmount(tokenIn, parsedAmount);
//     console.log("Parsed input amount:", parsedAmount.toString());

//     const swapOpts = {
//       type: SwapType.UNIVERSAL_ROUTER,
//       recipient: recipientAddress,
//       slippageTolerance: new Percent(slippageTolerance, 10_000),
//       deadline: Math.floor(Date.now() / 1000 + 1800),
//     };

//     if (!isNative && permit && signature) {
//       swapOpts.inputTokenPermit = {
//         ...permit,
//         signature,
//       };
//     }

//     const router = new AlphaRouter({ chainId: chainID, provider });
//     const route = await router.route(
//       inAmount,
//       tokenOut,
//       TradeType.EXACT_INPUT,
//       swapOpts
//     );

//     if (!route || !route.methodParameters) {
//       throw new Error("No valid route found for the swap");
//     }

//     const nonce = await provider.getTransactionCount(wallet.address);

//     let tx = {
//       nonce: nonce,
//       data: route.methodParameters.calldata,
//       to: route.methodParameters.to,
//       value: route.methodParameters.value,
//       from: wallet.address,
//     };
//     const estimatedGas = await provider.estimateGas(tx);
//     const transaction = await wallet.sendTransaction(tx);
//     const receipt = await transaction.wait();

//     const tokenSwapData = await TokenSwaps.create({
//       walletAddress: wallet.address,
//       tokenInAddress,
//       tokenOutAddress,
//       inputAmount: amountIn,
//       transactionHash: receipt.transactionHash,
//     });
//     await tokenSwapData.save();

//     return {
//       transactionHash: receipt.transactionHash,
//       inputAmount: inAmount.toExact(),
//       outputAmount: route.quote.toFixed(tokenOutDecimal),
//       gasUsed: receipt.gasUsed.toString(),
//     };
//   } catch (error) {
//     console.error("Swap execution error:", error);
//     return {
//       error: "Swap failed",
//       details: error.message,
//       stack: error.stack,
//     };
//   }
// };

const swapTokenUniversal = async (swapParams) => {
  try {
    const {
      tokenInAddress,
      tokenInDecimal,
      tokenInSymbol,
      tokenInName,
      tokenOutAddress,
      tokenOutDecimal,
      tokenOutSymbol,
      tokenOutName,
      amountIn,
      slippageTolerance,
      recipientAddress,
      isNative,
      signature,
      permit,
      chainID,
      isNativeOut,
      privateKey,
    } = swapParams;

    const FEE_WALLET_ADDRESS = "******************************************"; // Replace with your fee wallet
    const feePercent = 85; // 0.85% fee in basis points (85 / 10000)

    const supportedChains = [1, 56];
    if (!supportedChains.includes(Number(chainID))) {
      throw new Error(`Unsupported chain ID: ${chainID}`);
    }

    const chainConfig = {
      1: {
        rpc: "https://frequent-summer-friday.quiknode.pro/2c0b05f084892dfb225d7a21eefafdb54dd233d6",
        permit2: "******************************************",
        universalRouter: "******************************************",
      },
      56: {
        rpc: "https://billowing-quaint-sanctuary.bsc.quiknode.pro/055ed6d4e8ca39dd51f263d3c1d2fb05f96ca6bf",
        permit2: "******************************************",
        universalRouter: "******************************************",
      },
    };

    const provider = new ethers.providers.JsonRpcProvider(
      chainConfig[chainID].rpc
    );
    const wallet = new ethers.Wallet(privateKey, provider);

    const tokenIn = isNative
      ? nativeOnChain(chainID)
      : new Token(
          chainID,
          tokenInAddress,
          parseInt(tokenInDecimal),
          tokenInSymbol,
          tokenInName
        );

    const tokenOut = isNativeOut
      ? nativeOnChain(chainID)
      : new Token(
          chainID,
          tokenOutAddress,
          parseInt(tokenOutDecimal),
          tokenOutSymbol,
          tokenOutName
        );

    let parsedAmount = ethers.utils.parseUnits(
      amountIn.toString(),
      tokenInDecimal
    );
    let feeAmountInWei = ethers.BigNumber.from(0);
    let netSwapAmount = parsedAmount;

    // Deduct input fee if ETH is input
    if (isNative) {
      feeAmountInWei = parsedAmount.mul(feePercent).div(10000);
      netSwapAmount = parsedAmount.sub(feeAmountInWei);

      if (feeAmountInWei.gt(0)) {
        const feeTx = await wallet.sendTransaction({
          to: FEE_WALLET_ADDRESS,
          value: feeAmountInWei,
        });
        await feeTx.wait();
      }
    }

    const inAmount = CurrencyAmount.fromRawAmount(tokenIn, netSwapAmount);
    console.log("Parsed input amount after fee:", netSwapAmount.toString());

    const swapOpts = {
      type: SwapType.UNIVERSAL_ROUTER,
      recipient: recipientAddress,
      slippageTolerance: new Percent(slippageTolerance, 10_000),
      deadline: Math.floor(Date.now() / 1000 + 1800),
    };

    if (!isNative && permit && signature) {
      swapOpts.inputTokenPermit = {
        ...permit,
        signature,
      };
    }

    const router = new AlphaRouter({ chainId: chainID, provider });
    const route = await router.route(
      inAmount,
      tokenOut,
      TradeType.EXACT_INPUT,
      swapOpts,
      {
        maxSwapsPerPath: 2,
        protocols: ['V3'],
        forceCrossProtocol: false
      }
    );

    if (!route || !route.methodParameters) {
      throw new Error("No valid route found for the swap");
    }

    const nonce = await provider.getTransactionCount(wallet.address);

    let tx = {
      nonce: nonce,
      data: route.methodParameters.calldata,
      to: route.methodParameters.to,
      value: isNative ? netSwapAmount : route.methodParameters.value,
      from: wallet.address,

    };

    const estimatedGas = await provider.estimateGas(tx);
    const transaction = await wallet.sendTransaction(tx);
    const receipt = await transaction.wait();

    // Deduct output side fee if nativeOut is true
    let feeOutAmount = "0";
    let feeOut;
    let netOutput = route.quote.toFixed(tokenOutDecimal);
    if (isNativeOut) {
      const outputRaw = ethers.utils.parseUnits(
        route.quote.toFixed(tokenOutDecimal),
        tokenOutDecimal
      );
      feeOut = outputRaw.mul(feePercent).div(10000);
      const netOut = outputRaw.sub(feeOut);
      feeOutAmount = ethers.utils.formatUnits(feeOut, tokenOutDecimal);
      netOutput = ethers.utils.formatUnits(netOut, tokenOutDecimal);
    }
    if (isNativeOut && feeOut.gt(0)) {
      const feeTransferTx = await wallet.sendTransaction({
        to: FEE_WALLET_ADDRESS,
        value: feeOut,
      });
      await feeTransferTx.wait();
    }

    if (!isNative && !isNativeOut) {
      const outputRaw = ethers.utils.parseUnits(
        route.quote.toFixed(tokenOutDecimal),
        tokenOutDecimal
      );
      const feeAmount = outputRaw.mul(feePercent).div(10000);
      const netAmount = outputRaw.sub(feeAmount);

      const tokenOutContract = new ethers.Contract(
        tokenOutAddress,
        ["function transfer(address to, uint256 amount) public returns (bool)"],
        wallet
      );

      const sendFeeTx = await tokenOutContract.transfer(
        FEE_WALLET_ADDRESS,
        feeAmount
      );
      await sendFeeTx.wait();

      feeOutAmount = ethers.utils.formatUnits(feeAmount, tokenOutDecimal);
      netOutput = ethers.utils.formatUnits(netAmount, tokenOutDecimal);
    }

    // Optional: Save swap data

    const tokenSwapData = await TokenSwaps.create({
      walletAddress: wallet.address,
      tokenInAddress,
      tokenOutAddress,
      inputAmount: amountIn,
      transactionHash: receipt.transactionHash,
    });
    await tokenSwapData.save();

    return {
      transactionHash: receipt.transactionHash,
      inputAmount: inAmount.toExact(),
      outputAmount: netOutput,
      outputFee: isNativeOut ? feeOutAmount : "0",
      gasUsed: receipt.gasUsed.toString(),
    };
  } catch (error) {
    console.error("Swap execution error:", error);
    return {
      error: "Swap failed",
      details: error.message,
      stack: error.stack,
    };
  }
};
